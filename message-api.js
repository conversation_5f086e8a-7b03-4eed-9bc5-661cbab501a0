/**
 * واجهة برمجة التطبيقات (API) لإرسال الرسائل باستخدام مكتبة Baileys
 * هذا الملف يحتوي على وظائف معالجة طلبات إرسال الرسائل النصية والصور والملفات
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { Readable } = require('stream');
const tempUtils = require('./temp-utils'); // استيراد وظائف إدارة الملفات المؤقتة
const { updateOperationCompletionStatus, getConnectionData, addMessageResult } = require('./temp-utils'); // استيراد الدالة مباشرة
const utils = require('./utils');

// تخزين معرفات الرسائل مع العمليات لتتبع حالة الرسائل
const messageOperations = new Map(); // Map<operationId, { messageIds: Map<messageId, recipientInfo> }>

// دالة لحفظ معرف الرسالة مع العملية
function saveMessageIdForOperation(operationId, messageId, recipientInfo) {
    if (!messageOperations.has(operationId)) {
        messageOperations.set(operationId, { messageIds: new Map() });
    }

    const operation = messageOperations.get(operationId);
    operation.messageIds.set(messageId, recipientInfo);

    console.log(`Saved message ID ${messageId} for operation ${operationId}:`, recipientInfo);
}

// دالة للحصول على معلومات المستلم من معرف الرسالة
function getRecipientInfoByMessageId(messageId) {
    for (const [operationId, operation] of messageOperations.entries()) {
        if (operation.messageIds.has(messageId)) {
            return {
                operationId,
                recipientInfo: operation.messageIds.get(messageId)
            };
        }
    }
    return null;
}

// تصدير الدوال للاستخدام في ملفات أخرى
module.exports.saveMessageIdForOperation = saveMessageIdForOperation;
module.exports.getRecipientInfoByMessageId = getRecipientInfoByMessageId;

// إنشاء موجه للتعامل مع طلبات الرسائل
const router = express.Router();

// تكوين multer لتخزين الملفات المرفوعة
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        // إنشاء مجلد للملفات المؤقتة إذا لم يكن موجودًا
        const tempDir = path.join(__dirname, 'temp');
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        cb(null, tempDir);
    },
    filename: function (req, file, cb) {
        // إنشاء اسم فريد للملف
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ storage: storage });

/**
 * الحصول على الاتصال النشط للحساب
 * @param {string} accountName - اسم الحساب
 * @param {Object} connections - كائن يحتوي على اتصالات الحسابات
 * @returns {Object} - كائن الاتصال
 */
function getActiveConnection(accountName, connections) {
    try {
        console.log(`getActiveConnection: طلب الاتصال بحساب: "${accountName}"`);

        if (!accountName) {
            throw new Error('يرجى تحديد اسم الحساب');
        }

        if (!connections[accountName]) {
            throw new Error(`الحساب "${accountName}" غير موجود أو لم يتم تسجيل الدخول إليه`);
    }

        const connection = connections[accountName];

        // التحقق من حالة الاتصال
        if (!connection.client) {
            throw new Error(`لا يوجد عميل واتساب للحساب "${accountName}"`);
        }

        // التحقق من حالة الاتصال
        if (connection.status !== 'connected' && connection.status !== 'authenticated') {
            throw new Error(`الحساب "${accountName}" غير متصل حالياً. الحالة الحالية: ${connection.status}`);
        }

        // التحقق من وجود معلومات المستخدم
        if (!connection.client.info) {
            throw new Error('معلومات المستخدم غير متوفرة في جلسة واتساب. يرجى إعادة تسجيل الدخول');
         }

        console.log(`getActiveConnection: تم العثور على الاتصال النشط للحساب "${accountName}"`);
        return connection;
    } catch (error) {
        console.error('خطأ في دالة getActiveConnection:', error);
        throw error;
    }
}

/**
 * وظيفة مساعدة لتنسيق معرف المستلم
 * @param {object|string} recipient - معرف المستلم أو كائن يمثل المستلم (قد يحتوي على id)
 * @returns {string} - معرف المستلم المنسق (${number}@c.us أو ${groupId}@g.us)
 */
function formatRecipientId(recipient, accountName = null) {
    let recipientId;
    let isGroup = false;

    // دعم الكائنات التي تمثل مجموعة
    if (typeof recipient === 'object' && recipient !== null) {
        if (recipient.type === 'group') {
            isGroup = true;
            if (recipient.id && typeof recipient.id === 'string' && recipient.id.endsWith('@g.us')) {
        recipientId = recipient.id;
            } else if (recipient.name && accountName) {
                // محاولة جلب معرف المجموعة من ملف المجموعات
                try {
                    const groupsPath = require('path').join(__dirname, 'sessions', accountName, 'groups.json');
                    if (fs.existsSync(groupsPath)) {
                        const groupsData = JSON.parse(fs.readFileSync(groupsPath, 'utf8'));
                        // دعم كل من المصفوفة أو الكائن
                        let groupObj = null;
                        if (Array.isArray(groupsData)) {
                            groupObj = groupsData.find(g => g.name === recipient.name);
    } else {
                            groupObj = Object.values(groupsData).find(g => g.name === recipient.name);
                        }
                        if (groupObj && groupObj.id && groupObj.id.endsWith('@g.us')) {
                            recipientId = groupObj.id;
                        } else {
                            throw new Error('لم يتم العثور على معرف المجموعة بناءً على الاسم: ' + recipient.name);
                        }
                    } else {
                        throw new Error('ملف بيانات المجموعات غير موجود لهذا الحساب');
                    }
                } catch (err) {
                    console.error('formatRecipientId: خطأ في جلب معرف المجموعة من ملف المجموعات:', err.message);
                    throw new Error('لم يتم العثور على معرف المجموعة بناءً على الاسم: ' + recipient.name);
                }
            } else {
                throw new Error('معرف المجموعة غير صالح أو غير موجود. يجب أن يكون معرف المجموعة موجوداً وينتهي بـ @g.us');
            }
        } else if (recipient.id) {
            recipientId = recipient.id;
        }
    }
    // دعم النصوص
    if (!recipientId && typeof recipient === 'string') {
        recipientId = recipient;
        if (recipientId.endsWith('@g.us')) {
            isGroup = true;
        }
    }
    // إذا لم يتم تحديد recipientId بعد
    if (!recipientId) {
         console.error('formatRecipientId: خطأ - معرف المستلم غير موجود أو بتنسيق غير صحيح:', recipient);
        throw new Error('معرف المستلم غير موجود أو بتنسيق غير صحيح');
    }

    // تسجيل معرف المستلم للتشخيص
    console.log('formatRecipientId: معرف المستلم الأصلي:', recipientId, 'من النوع:', typeof recipientId, 'isGroup:', isGroup);

    // إذا كان مجموعة، يجب التأكد أن المعرف صحيح
    if (isGroup) {
        if (!recipientId.endsWith('@g.us') || recipientId.length < 5) {
            console.error('formatRecipientId: خطأ - معرف المجموعة غير صالح:', recipientId);
            throw new Error('معرف المجموعة غير صالح - يجب أن يكون معرف مجموعة واتساب حقيقي');
        }
        console.log('formatRecipientId: تم التعرف على معرف مجموعة:', recipientId);
        return recipientId;
    }

    // باقي الحالات للأفراد فقط
    if (recipientId.endsWith('@c.us')) {
         console.log('formatRecipientId: تم التعرف على معرف فردي:', recipientId);
         return recipientId;
    } else if (recipientId.includes('@s.whatsapp.net')) {
        const numberPart = recipientId.split('@')[0];
        const formattedId = `${numberPart}@c.us`;
         console.log('formatRecipientId: تم تحويل معرف @s.whatsapp.net إلى @c.us:', formattedId);
        return formattedId;
    } else {
        // هنا فقط نتحقق من الرقم إذا لم يكن مجموعة
        const cleanedRecipient = recipientId.replace(/[^0-9]/g, '');
        if (cleanedRecipient.length < 10 || cleanedRecipient.length > 15) {
            console.error('formatRecipientId: خطأ - رقم الهاتف غير صالح:', cleanedRecipient);
            throw new Error('رقم الهاتف غير صالح - يجب أن يكون بين 10 و 15 رقم');
        }
        const formattedId = `${cleanedRecipient}@c.us`;
        console.log('formatRecipientId: تم تنسيق رقم هاتف إلى معرف فردي:', formattedId);
        return formattedId;
    }
}

/**
 * وظيفة مساعدة لإرسال رسالة نصية
 * @param {object} sock - كائن اتصال واتساب (whatsapp-web.js Client)
 * @param {string} recipientId - معرف المستلم (${number}@c.us أو ${groupId}@g.us)
 * @param {string} text - نص الرسالة
 * @returns {Promise<object>} - وعد بنتيجة الإرسال
 */
async function sendText(sock, recipient, text, accountName = null) {
    const recipientId = formatRecipientId(recipient, accountName);
    try {
        // التحقق من صحة الجلسة والمعرف والنص
        if (!sock || typeof sock.sendMessage !== 'function') {
            throw new Error('لا توجد جلسة واتساب نشطة أو جاهزة للإرسال. يرجى التأكد من تسجيل الدخول.');
        }
        if (!recipientId || typeof recipientId !== 'string') {
            throw new Error('معرف المستلم غير صالح');
        }
        if (!text || typeof text !== 'string' || text.trim().length === 0) {
            throw new Error('نص الرسالة غير صالح أو فارغ');
        }
        if (text.length > 4096) {
            throw new Error('نص الرسالة أطول من الحد المسموح به (4096 حرف)');
        }
        const result = await sock.sendMessage(recipientId, text);
        if (result && result.id && result.id.id) {
             return {
                success: true,
                messageId: result.id.id,
                recipient: recipientId,
                timestamp: result.timestamp || Date.now() / 1000,
                status: 'sent',
                whatsappMessageObject: result // إضافة كائن الرسالة الكامل من WhatsApp
            };
        } else {
            return {
                success: false,
                recipient: recipientId,
                error: 'لم يتم تأكيد إرسال الرسالة من واتساب (معرف الرسالة غير متوفر)',
                status: 'failed'
            };
        }
    } catch (error) {
        return {
            success: false,
            recipient: recipientId,
            error: error.message,
            status: 'failed'
        };
    }
}

/**
 * وظيفة مساعدة لإرسال صورة
 * @param {object} sock - كائن اتصال واتساب (whatsapp-web.js Client)
 * @param {string} recipientId - معرف المستلم
 * @param {string} imagePath - مسار الصورة المحلي
 * @param {string} caption - نص التعليق (اختياري)
 * @returns {Promise<object>} - وعد بنتيجة الإرسال
 */
async function sendImage(sock, recipient, imagePath, caption = '', accountName = null) {
    const recipientId = formatRecipientId(recipient, accountName);
    try {
        if (!sock || typeof sock.sendMessage !== 'function') {
             throw new Error('لا توجد جلسة واتساب نشطة أو جاهزة للإرسال. يرجى التأكد من تسجيل الدخول.');
        }
        if (!recipientId || typeof recipientId !== 'string') {
             throw new Error('معرف المستلم غير صالح');
        }
        if (!imagePath || typeof imagePath !== 'string') {
             throw new Error('مسار الصورة غير صالح');
        }
        if (!fs.existsSync(imagePath)) {
            throw new Error(`ملف الصورة غير موجود: ${imagePath}`);
        }
        const { MessageMedia } = require('whatsapp-web.js');
        let messageMedia = MessageMedia.fromFilePath(imagePath);
             if (!messageMedia.filename) {
                 messageMedia.filename = path.basename(imagePath);
             }
        const options = {};
        if (caption) options.caption = caption;
        const result = await sock.sendMessage(recipientId, messageMedia, options);
        if (result && result.id && result.id.id) {
            return {
                success: true,
                messageId: result.id.id,
                recipient: recipientId,
                mediaType: 'image',
                caption: caption || null,
                timestamp: result.timestamp || Date.now() / 1000,
                status: 'sent'
            };
        } else {
             return {
                success: false,
                 recipient: recipientId,
                 mediaType: 'image',
                 caption: caption || null,
                error: 'لم يتم تأكيد إرسال الصورة من واتساب (معرف الرسالة غير متوفر)',
                status: 'failed'
             };
        }
    } catch (error) {
        return {
            success: false,
            recipient: recipientId,
            mediaType: 'image',
            caption: caption || null,
            error: error.message,
            status: 'failed'
        };
    } finally {
         if (imagePath && fs.existsSync(imagePath)) {
             fs.unlink(imagePath, (err) => {
                 if (err) {
                     console.error(`sendImage: خطأ في حذف الملف المؤقت ${imagePath}:`, err);
                 }
             });
         }
    }
}

/**
 * وظيفة مساعدة لإرسال ملف (غير صورة)
 * @param {object} sock - كائن اتصال واتساب (whatsapp-web.js Client)
 * @param {string} recipientId - معرف المستلم
 * @param {string} filePath - مسار الملف المحلي
 * @param {string} caption - نص التعليق (اختياري)
 * @returns {Promise<object>} - وعد بنتيجة الإرسال
 */
async function sendFile(sock, recipient, filePath, caption = '', accountName = null, detectedType = null) {
    const recipientId = formatRecipientId(recipient, accountName);
    try {
         if (!fs.existsSync(filePath)) {
             throw new Error(`الملف غير موجود: ${filePath}`);
         }
        const { MessageMedia } = require('whatsapp-web.js');
        let messageMedia = MessageMedia.fromFilePath(filePath);
        if (!messageMedia.filename) {
            messageMedia.filename = path.basename(filePath);
        }
        let options = { caption: caption || undefined };
        // تحديد نوع الإرسال بناءً على نوع الملف
        if (detectedType && detectedType.mime) {
            if (detectedType.mime === 'application/pdf' || detectedType.mime.startsWith('application/vnd')) {
                options.sendMediaAsDocument = true;
            } else if (detectedType.mime.startsWith('video/')) {
                options.sendMediaAsVideo = true;
            } else if (detectedType.mime.startsWith('audio/')) {
                options.sendMediaAsAudio = true;
            } else {
                options.sendMediaAsDocument = true;
            }
        } else {
            options.sendMediaAsDocument = true;
        }
        const result = await sock.sendMessage(recipientId, messageMedia, options);
        if (result && result.id && result.id.id) {
            return {
                success: true,
                messageId: result.id.id,
                recipient: recipientId,
                fileName: path.basename(filePath),
                caption: caption || null,
                timestamp: result.timestamp || Date.now() / 1000,
                 status: 'sent'
            };
        } else {
             return {
                success: false,
                 recipient: recipientId,
                fileName: path.basename(filePath),
                 caption: caption || null,
                error: 'لم يتم تأكيد إرسال الملف من واتساب (معرف الرسالة غير متوفر)',
                status: 'failed'
             };
        }
    } catch (error) {
        return {
            success: false,
            recipient: recipientId,
            fileName: path.basename(filePath),
            caption: caption || null,
            error: error.message,
            status: 'failed'
        };
    } finally {
        if (filePath && fs.existsSync(filePath)) {
            fs.unlink(filePath, (err) => {
                if (err) {
                    console.error(`sendFile: خطأ في حذف الملف المؤقت ${filePath}:`, err);
                }
            });
        }
    }
}

/**
 * API لإرسال مجلد من الملفات (مجموعة ملفات) إلى مستلم واحد
 * هذه الوظيفة ترسل كل ملف داخل المجلد بشكل فردي
 */
router.post('/send-folder/:accountName', upload.array('files'), async (req, res) => {
    // تعريف المتغير files في النطاق الأعلى ليكون متاحًا في جميع أجزاء الدالة
    let files = [];
    // إنشاء معرف فريد للعملية لتتبعها
    let operationId = req.body.operationId || 'op-' + Date.now() + '-' + Math.floor(Math.random() * 1E9);
    let accountName = req.params.accountName;

    try {
        // إنشاء بيانات العملية الأولية
        const initialOperationData = {
            operationId,
            accountName,
            startTime: Date.now(),
            status: 'processing',
            messageType: 'folder',
            recipientsCount: req.body.recipients ? (Array.isArray(JSON.parse(req.body.recipients)) ? JSON.parse(req.body.recipients).length : 1) : 0,
            filesCount: req.files ? req.files.length : 0,
            success: null,
            successCount: 0,
            failureCount: 0,
            results: []
        };

        // حفظ بيانات العملية في الملف المؤقت
        tempUtils.saveConnectionData(operationId, initialOperationData);
        console.log(`تم إنشاء بيانات العملية ${operationId} بنجاح`);

    // إرسال استجابة أولية فوراً لبدء العملية في الخلفية
    res.json({
        success: true,
        operationId: operationId,
        message: 'تم بدء عملية إرسال المجلد',
        status: 'processing',
        totalFiles: req.files ? req.files.length : 0,
            totalRecipients: initialOperationData.recipientsCount
    });

        files = req.files || []; // الملفات التي تم تحميلها

    // تشغيل عملية الإرسال الفعلية في الخلفية
    (async () => {
        try {
            // التحقق من معلمات الطلب
        console.log('API /send-folder: النوع الأصلي لاسم الحساب:', typeof accountName, accountName);
        if (accountName && typeof accountName !== 'string') {
            console.warn('API /send-folder: تم استلام اسم حساب غير نصي، محاولة تحويله إلى نص.');
            accountName = String(accountName).trim();
            console.log('API /send-folder: تم تحويل اسم الحساب إلى:', accountName);
        }
            // استخدام accountName المعالج
            const recipients = req.body.recipients ? JSON.parse(req.body.recipients) : [];
            const caption = req.body.caption || '';
             const messageInterval = req.body.messageInterval || 3; // الحصول على الفاصل الزمني


            console.log('/send-folder API: معلمات الطلب بعد المعالجة:', {
                accountName,
                recipientsCount: recipients.length,
                captionProvided: !!caption,
                filesReceived: files.length,
                operationId: operationId
            });

             if (recipients.length === 0) {
                  console.error('/send-folder API: No recipients provided.');
                  // تنظيف الملفات المرفوعة إذا وجدت
                  files.forEach(file => {
                      if (fs.existsSync(file.path)) {
                          fs.unlinkSync(file.path);
                      }
                  });
                 updateOperationCompletionStatus(operationId, 0, 0, 0, [], 'لم يتم تحديد مستلم واحد على الأقل.');
                 return; // الخروج من الدالة الخلفية
             }

            if (files.length === 0) {
                 console.error('/send-folder API: No files uploaded.');
                 updateOperationCompletionStatus(operationId, recipients.length, 0, recipients.length, [], 'لم يتم تحميل أي ملفات.');
                 return; // الخروج من الدالة الخلفية
            }


             // ** إضافة منطق الانتظار وإعادة المحاولة هنا **
             let connectionReady = false;
             const maxRetries = 10; // عدد مرات إعادة المحاولة
             const retryDelay = 2000; // التأخير بين المحاولات بالملي ثانية (2 ثانية)
             let currentRetry = 0;
             let activeConnection = null;

             while (currentRetry < maxRetries && !connectionReady) {
                 try {
                     activeConnection = getActiveConnection(accountName, req.app.get('whatsappConnections'));
                     // إذا وصلت هنا، فالاتصال متاح ومبدئياً جاهز
                     connectionReady = true;
                     console.log(`API /send-folder: الاتصال بالحساب ${accountName} جاهز بعد ${currentRetry} محاولة.`);
                 } catch (connError) {
                     console.warn(`API /send-folder: محاولة ${currentRetry + 1}/${maxRetries} للحصول على الاتصال بالحساب ${accountName} فشلت: ${connError.message}`);
                     currentRetry++;
                     if (currentRetry < maxRetries) {
                         await new Promise(resolve => setTimeout(resolve, retryDelay));
                     } else {
                         // إذا فشلت جميع المحاولات
                         throw connError; // أعد رمي الخطأ ليتم التقاطه في catch الخارجية
                     }
                 }
             }

             if (!activeConnection || !activeConnection.sock) {
                 throw new Error('فشل في الحصول على اتصال واتساب نشط بعد عدة محاولات.');
             }

             const sock = activeConnection.sock;


            let totalSuccessCount = 0;
            let totalFailCount = 0;
            let allResults = [];

            // إرسال جميع الملفات إلى كل مستلم على حدة
            for (let recipientIndex = 0; recipientIndex < recipients.length; recipientIndex++) {
                const recipient = recipients[recipientIndex];
                let recipientSuccessCount = 0;
                let recipientFailCount = 0;
                let recipientResults = [];

                console.log(`/send-folder API: جاري إرسال ${files.length} ملف إلى المستلم ${recipient.name} (${recipientIndex + 1}/${recipients.length})`);

                for (let fileIndex = 0; fileIndex < files.length; fileIndex++) {
                    const file = files[fileIndex];
                    const tempFilePath = file.path;
                    const fileName = file.originalname;
                    let detectedType = null;
                    try {
                        // فحص نوع الملف الحقيقي
                        const { fileTypeFromFile } = await import('file-type');
                        detectedType = await fileTypeFromFile(tempFilePath);
                        console.log(`نوع الملف الحقيقي:`, detectedType);
                        let result;
                        if (detectedType && detectedType.mime.startsWith('image/')) {
                            result = await sendImage(sock, recipient, tempFilePath, caption, accountName);
                            result.mediaType = 'image';
                        } else {
                            result = await sendFile(sock, recipient, tempFilePath, caption, accountName, detectedType);
                            result.mediaType = detectedType ? detectedType.mime : 'file';
                        }
                        result.originalFileName = fileName;
                        recipientResults.push(result);
                        if (result.success) {
                            recipientSuccessCount++;
                        } else {
                            recipientFailCount++;
                            console.error(`/send-folder API: فشل إرسال ملف ${fileName} إلى ${recipient.name}: ${result.error}`);
                        }
                        // تحديث ملف العملية بعد كل ملف
                        const operationData = tempUtils.getConnectionData(operationId);
                        operationData.recipientsCount = recipients.length;
                        operationData.successCount = (operationData.successCount || 0) + (result.success ? 1 : 0);
                        operationData.failureCount = (operationData.failureCount || 0) + (!result.success ? 1 : 0);
                        operationData.processedCount = (operationData.processedCount || 0) + 1;
                        tempUtils.saveConnectionData(operationId, operationData);
                    } catch (error) {
                        console.error(`/send-folder API: خطأ فردي أثناء إرسال ملف ${fileName} إلى ${recipient.name}:`, error);
                        recipientResults.push({
                            success: false,
                            recipient: recipient,
                            error: error.message,
                            mediaType: detectedType ? detectedType.mime : 'file',
                            originalFileName: fileName,
                            caption: caption || null,
                            status: 'failed'
                        });
                        recipientFailCount++;
                        // تحديث ملف العملية بعد كل ملف (في حالة الخطأ)
                        const operationData = tempUtils.getConnectionData(operationId);
                        operationData.recipientsCount = recipients.length;
                        operationData.successCount = (operationData.successCount || 0) + (!result.success ? 1 : 0);
                        operationData.failureCount = (operationData.failureCount || 0) + (result.success ? 1 : 0);
                        operationData.processedCount = (operationData.processedCount || 0) + 1;
                        tempUtils.saveConnectionData(operationId, operationData);
                    }
                }

                totalSuccessCount += recipientSuccessCount;
                totalFailCount += recipientFailCount;
                allResults.push({
                    recipient: recipient,
                    successCount: recipientSuccessCount,
                    failCount: recipientFailCount,
                    results: recipientResults
                });

                // تطبيق الفاصل الزمني بين المستلمين إذا لم يكن المستلم الأخير
                if (recipientIndex < recipients.length - 1) {
                    console.log(`/send-folder API: انتظار ${messageInterval} ثوانٍ قبل الانتقال للمستلم التالي...`);
                    await new Promise(resolve => setTimeout(resolve, messageInterval * 1000));
                }
            }


            console.log('/send-folder API: Batch sending complete.', {
                totalRecipients: recipients.length,
                totalFilesProcessed: files.length * recipients.length,
                totalSuccessCount: totalSuccessCount,
                totalFailCount: totalFailCount
            });

             // تسجيل النتيجة النهائية للعملية باستخدام operationId
             // يمكن هنا دمج النتائج الفردية لكل ملف ومستلم أو تلخيصها
             updateOperationCompletionStatus(operationId, recipients.length * files.length, totalSuccessCount, totalFailCount, allResults);

        } catch (error) {
            console.error('/send-folder API: خطأ عام في معالجة إرسال المجلد:', error);

             // في حالة حدوث خطأ قبل البدء بالإرسال الفردي للملفات أو المستلمين
             const totalPossibleSends = recipients.length * (files ? files.length : 0);
             updateOperationCompletionStatus(operationId, totalPossibleSends, totalSuccessCount, totalPossibleSends - totalSuccessCount, allResults, error.message);

             // إذا لم يتم إرسال استجابة أولية بعد (وهو غير مرجح هنا لأنها ترسل فوراً)،
             // يمكن التعامل معها ولكن API هذه ترسل استجابة فوراً.
        } finally {
            // تنظيف جميع الملفات المؤقتة بعد الانتهاء من معالجة الطلب
            // هذه خطوة مهمة لضمان عدم تراكم الملفات في مجلد temp
            if (files && files.length > 0) {
        files.forEach(file => {
                    if (fs.existsSync(file.path)) {
            fs.unlink(file.path, (err) => {
                if (err) {
                                console.error(`API /send-folder: خطأ في حذف الملف المؤقت ${file.path}:`, err);
                            } else {
                                console.log(`API /send-folder: تم حذف الملف المؤقت: ${file.path}`);
                            }
                        });
                    } else {
                         console.warn(`API /send-folder: لم يتم العثور على مسار ملف صالح لحذفه: ${file.path}`);
                    }
                });
            }
        }
    })(); // تشغيل الدالة اللامسماة على الفور
    } catch (error) {
        console.error('خطأ في إنشاء بيانات العملية:', error);
        res.status(500).json({
            success: false,
            error: 'فشل في إنشاء بيانات العملية: ' + error.message
        });
    }
});

// API لإرسال رسالة نصية
router.post('/send-message/:accountName', async (req, res) => {
    let operationId = req.body.operationId || 'op-' + Date.now() + '-' + Math.floor(Math.random() * 1E9);
    let accountName = req.params.accountName;
    const recipients = req.body.recipients || [];
    const messageText = req.body.message ? req.body.message.text : '';
        const messageInterval = req.body.messageInterval || 3;

        // إنشاء بيانات العملية الأولية
        const initialOperationData = {
            operationId,
            accountName,
            startTime: Date.now(),
            status: 'processing',
            messageType: 'text',
            recipientsCount: recipients.length,
            success: null,
            successCount: 0,
            failureCount: 0,
            results: []
        };

        // حفظ بيانات العملية في الملف المؤقت
        tempUtils.saveConnectionData(operationId, initialOperationData);
        console.log(`تم إنشاء بيانات العملية ${operationId} بنجاح`);

    console.log(`/api/send-message/:${accountName} - Request received`, { operationId, recipientsCount: recipients.length, messageTextLength: messageText.length });

    if (!recipients || recipients.length === 0) {
        console.error(`/api/send-message/:${accountName} - No recipients provided.`);
        return res.status(400).json({ success: false, error: 'لم يتم تحديد مستلم واحد على الأقل.', operationId });
    }

    if (!messageText) {
         console.error(`/api/send-message/:${accountName} - Message text is empty.`);
        return res.status(400).json({ success: false, error: 'نص الرسالة فارغ.', operationId });
    }

    // إرسال استجابة أولية فوراً لبدء العملية في الخلفية إذا لم يتم تتبعها بالفعل
    // إذا كان operationId موجوداً، هذا يعني أن العملية بدأت بالفعل من الأمام ويتم تتبعها هناك.
    // لا نرسل استجابة هنا لتجنب إغلاق الاتصال قبل انتهاء العملية إذا كانت فردية.
    // إذا كانت العملية جماعية (من sendMessageToAll)، فإن operationId يُرسل ويتم التعامل مع الاستجابة الأولية في sendMessageToAll.

    // تشغيل عملية الإرسال الفعلية في الخلفية
    (async () => {
        let results = [];
        let successCount = 0;
        let failCount = 0;

        try {
            // ** إضافة منطق الانتظار وإعادة المحاولة هنا **
            let connectionReady = false;
            const maxRetries = 10; // عدد مرات إعادة المحاولة
            const retryDelay = 2000; // التأخير بين المحاولات بالملي ثانية (2 ثانية)
            let currentRetry = 0;
            let activeConnection = null;

            while (currentRetry < maxRetries && !connectionReady) {
                try {
                    activeConnection = getActiveConnection(accountName, req.app.get('whatsappConnections'));
                    // إذا وصلت هنا، فالاتصال متاح ومبدئياً جاهز
                    connectionReady = true;
                    console.log(`API /send-message: الاتصال بالحساب ${accountName} جاهز بعد ${currentRetry} محاولة.`);
                } catch (connError) {
                    console.warn(`API /send-message: محاولة ${currentRetry + 1}/${maxRetries} للحصول على الاتصال بالحساب ${accountName} فشلت: ${connError.message}`);
                    currentRetry++;
                    if (currentRetry < maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, retryDelay));
                    } else {
                        // إذا فشلت جميع المحاولات
                        throw connError; // أعد رمي الخطأ ليتم التقاطه في catch الخارجية
                    }
                }
            }

            if (!activeConnection || !activeConnection.sock) {
                throw new Error('فشل في الحصول على اتصال واتساب نشط بعد عدة محاولات.');
            }

            const sock = activeConnection.sock;

        for (let i = 0; i < recipients.length; i++) {
            const recipient = recipients[i];
                    const recipientId = formatRecipientId(recipient, accountName);

                try {
                     // معالجة العنصر النائب لاسم المستلم في الرسالة النصية
                     let personalizedMessageText = messageText;
                if (recipient && recipient.name) {
                         personalizedMessageText = personalizedMessageText.replace(/{recipient_name}/g, recipient.name);
                         console.log(`API /send-message: تم استبدال اسم المستلم في الرسالة النصية لـ ${recipientId}.`);
                     } else {
                         // إذا لم يتوفر اسم المستلم، يمكن إزالة العنصر النائب أو تركه حسب الرغبة
                         personalizedMessageText = personalizedMessageText.replace(/{recipient_name}/g, ''); // إزالة العنصر النائب
                         console.log(`API /send-message: لم يتم العثور على اسم المستلم لـ ${recipientId}، تم إزالة العنصر النائب.`);
                     }


                        const result = await sendText(sock, recipientId, personalizedMessageText, accountName);

                    // حفظ معرف الرسالة مع العملية إذا نجح الإرسال
                    if (result.success && result.messageId) {
                        saveMessageIdForOperation(operationId, result.messageId, {
                            recipientIndex: i,
                            recipient: recipient,
                            accountName: accountName,
                            messageType: 'text'
                        });
                    }

                    results.push(result);
                    if (result.success) {
                        successCount++;
                            if (operationId) {
                                addMessageResult(operationId, {
                                    recipient: recipient.id || recipient.name,
                                    recipientName: recipient.name || recipient.id,
                                    success: true,
                                    messageId: result.messageId,
                                    type: 'text',
                                    processedCount: i + 1,
                                    totalCount: recipients.length
                                });
                            }
                    } else {
                        failCount++;
                         console.error(`API /send-message: فشل إرسال الرسالة النصية إلى ${recipientId}: ${result.error}`);
                            if (operationId) {
                                addMessageResult(operationId, {
                                    recipient: recipient.id || recipient.name,
                                    recipientName: recipient.name || recipient.id,
                                    success: false,
                                    error: result.error,
                                    type: 'text',
                                    processedCount: i + 1,
                                    totalCount: recipients.length
                                });
                            }
                }
            } catch (error) {
                    console.error(`API /send-message: خطأ فردي أثناء إرسال الرسالة إلى ${recipientId}:`, error);
                    results.push({
                        success: false,
                        recipient: recipient,
                        error: error.message,
                        status: 'failed',
                        type: 'text'
                    });
                    failCount++;
                    if (operationId) {
                        addMessageResult(operationId, {
                            recipient: recipient.id || recipient.name,
                            recipientName: recipient.name || recipient.id,
                            success: false,
                            error: error.message,
                            type: 'text',
                            processedCount: i + 1,
                            totalCount: recipients.length
                        });
                    }
                }

                // تطبيق الفاصل الزمني بين الرسائل إذا لم تكن الرسالة الأخيرة
                if (i < recipients.length - 1) {
                    console.log(`API /send-message: انتظار ${messageInterval} ثوانٍ قبل إرسال الرسالة التالية...`);
                    await new Promise(resolve => setTimeout(resolve, messageInterval * 1000));
                }
            }

            // إرسال النتائج النهائية بعد اكتمال الإرسال لجميع المستلمين
            // يتم إرسال هذه الاستجابة فقط إذا لم يتم التعامل مع الاستجابة الأولية في الأمام (أي لم يتم إرسال operationId في الطلب الأصلي)
            // أو إذا كانت هذه هي نهاية المعالجة الخلفية لعملية جماعية.
            // في حالة العملية الجماعية التي بدأت من الأمام (مع operationId)، فإن تحديث حالة العملية في الأمام هو المسؤولية
            // الرئيسية، لكن إرجاع النتائج هنا يساعد في التسجيل.
            console.log(`/api/send-message/:${accountName} - Batch sending complete. Success: ${successCount}, Failed: ${failCount}`);

            // تسجيل النتيجة النهائية للعملية باستخدام operationId
             updateOperationCompletionStatus(operationId, recipients.length, successCount, failCount, results);


    } catch (error) {
             console.error(`/api/send-message/:${accountName} - خطأ عام في معالجة الإرسال:`, error);

             // في حالة حدوث خطأ قبل البدء بالإرسال الفردي (مثل عدم الحصول على الاتصال)
             // قم بتحديث حالة العملية إلى فشل لجميع المستلمين
             updateOperationCompletionStatus(operationId, recipients.length, successCount, recipients.length - successCount, results, error.message);

             // إذا لم يتم إرسال استجابة أولية بعد، قم بإرسال استجابة خطأ الآن
             if (!res.headersSent) {
                 res.status(500).json({
                     success: false,
                    error: error.message,
                     operationId: operationId,
                     results: results // قد تكون قائمة فارغة أو جزئية هنا
                 });
             }
        } finally {
             // تنظيف أي موارد مؤقتة إذا لزم الأمر
        }
    })(); // تشغيل الدالة اللامسماة على الفور

     // إذا تم إرسال operationId من الأمام، أرسل استجابة أولية فورية للإشارة إلى بدء المعالجة
     // وإلا، انتظر حتى تنتهي العملية في الخلفية لإرسال الاستجابة النهائية.
     if (req.body.operationId && !res.headersSent) {
        console.log(`/api/send-message/:${accountName} - Sending initial response for operation ${operationId}.`);
         res.json({
             success: true,
             operationId: operationId,
             message: 'تم بدء عملية إرسال الرسائل',
             status: 'processing',
             totalRecipients: recipients.length
         });
    }
});

// API لإرسال صورة
router.post('/send-image/:accountName', upload.single('image'), async (req, res) => {
    let operationId = req.body.operationId || 'op-' + Date.now() + '-' + Math.floor(Math.random() * 1E9);
    let accountName = req.params.accountName;
    const recipients = req.body.recipients ? JSON.parse(req.body.recipients) : [];
    const caption = req.body.caption || '';
    const imageFile = req.file;
    const messageInterval = req.body.messageInterval || 3;

    // إنشاء بيانات العملية الأولية
    const initialOperationData = {
        operationId,
        accountName,
        startTime: Date.now(),
        status: 'processing',
        messageType: 'image',
        recipientsCount: recipients.length,
        success: null,
        successCount: 0,
        failureCount: 0,
        results: []
    };

    // حفظ بيانات العملية في الملف المؤقت
    tempUtils.saveConnectionData(operationId, initialOperationData);
    console.log(`تم إنشاء بيانات العملية ${operationId} بنجاح`);

    console.log(`/api/send-image/:${accountName} - Request received`, { operationId, recipientsCount: recipients.length, captionProvided: !!caption, imageFile: imageFile ? imageFile.originalname : 'none' });

    if (!recipients || recipients.length === 0) {
         console.error(`/api/send-image/:${accountName} - No recipients provided.`);
        // تنظيف الملف المرفوع إذا وجد
        if (imageFile && fs.existsSync(imageFile.path)) {
            fs.unlinkSync(imageFile.path);
        }
        return res.status(400).json({ success: false, error: 'لم يتم تحديد مستلم واحد على الأقل.', operationId });
    }

    if (!imageFile) {
        console.error(`/api/send-image/:${accountName} - No image file uploaded.`);
        return res.status(400).json({ success: false, error: 'لم يتم تحميل ملف الصورة.', operationId });
    }

     // إرسال استجابة أولية فوراً لبدء العملية في الخلفية إذا لم يتم تتبعها بالفعل
     if (req.body.operationId && !res.headersSent) {
        console.log(`/api/send-image/:${accountName} - Sending initial response for operation ${operationId}.`);
         res.json({
             success: true,
             operationId: operationId,
             message: 'تم بدء عملية إرسال الصورة',
             status: 'processing',
             totalRecipients: recipients.length
         });
     }

    // تشغيل عملية الإرسال الفعلية في الخلفية
    (async () => {
        let results = [];
        let successCount = 0;
        let failCount = 0;
        let tempFilePath = imageFile.path; // مسار الملف المؤقت

        try {
             // ** إضافة منطق الانتظار وإعادة المحاولة هنا **
             let connectionReady = false;
             const maxRetries = 10; // عدد مرات إعادة المحاولة
             const retryDelay = 2000; // التأخير بين المحاولات بالملي ثانية (2 ثانية)
             let currentRetry = 0;
             let activeConnection = null;

             while (currentRetry < maxRetries && !connectionReady) {
                 try {
                     activeConnection = getActiveConnection(accountName, req.app.get('whatsappConnections'));
                     // إذا وصلت هنا، فالاتصال متاح ومبدئياً جاهز
                     connectionReady = true;
                     console.log(`API /send-image: الاتصال بالحساب ${accountName} جاهز بعد ${currentRetry} محاولة.`);
                 } catch (connError) {
                     console.warn(`API /send-image: محاولة ${currentRetry + 1}/${maxRetries} للحصول على الاتصال بالحساب ${accountName} فشلت: ${connError.message}`);
                     currentRetry++;
                     if (currentRetry < maxRetries) {
                         await new Promise(resolve => setTimeout(resolve, retryDelay));
                     } else {
                         // إذا فشلت جميع المحاولات
                         throw connError; // أعد رمي الخطأ ليتم التقاطه في catch الخارجية
                     }
                 }
             }

             if (!activeConnection || !activeConnection.sock) {
                 throw new Error('فشل في الحصول على اتصال واتساب نشط بعد عدة محاولات.');
             }

             const sock = activeConnection.sock;

            for (let i = 0; i < recipients.length; i++) {
                const recipient = recipients[i];
                const recipientId = formatRecipientId(recipient, accountName);

                try {
                    const result = await sendImage(sock, recipient, tempFilePath, caption, accountName);
                    results.push(result);
                    if (result.success) {
                        successCount++;
                        if (operationId) {
                            addMessageResult(operationId, { recipient: recipient.id || recipient.name, success: true, messageId: result.messageId });
                        }
                    } else {
                         failCount++;
                         console.error(`API /send-image: فشل إرسال الصورة إلى ${recipient.name}: ${result.error}`);
                        if (operationId) {
                            addMessageResult(operationId, { recipient: recipient.id || recipient.name, success: false, error: result.error });
                        }
                }
            } catch (error) {
                     console.error(`API /send-image: خطأ فردي أثناء إرسال الصورة إلى ${recipient.name}:`, error);
                    results.push({
                    success: false,
                        recipient: recipient,
                        error: error.message,
                        mediaType: 'image',
                        caption: caption || null,
                        status: 'failed'
                    });
                    failCount++;
                    if (operationId) {
                        addMessageResult(operationId, { recipient: recipient.id || recipient.name, success: false, error: error.message });
                    }
                }

                 // تطبيق الفاصل الزمني بين الرسائل إذا لم تكن الرسالة الأخيرة
                 if (i < recipients.length - 1) {
                     console.log(`API /send-image: انتظار ${messageInterval} ثوانٍ قبل إرسال الرسالة التالية...`);
                    await new Promise(resolve => setTimeout(resolve, messageInterval * 1000));
                }
            }

             console.log(`/api/send-image/:${accountName} - Batch sending complete. Success: ${successCount}, Failed: ${failCount}`);

             // تسجيل النتيجة النهائية للعملية باستخدام operationId
             updateOperationCompletionStatus(operationId, recipients.length, successCount, failCount, results);


        } catch (error) {
             console.error(`/api/send-image/:${accountName} - خطأ عام في معالجة الإرسال:`, error);

             // في حالة حدوث خطأ قبل البدء بالإرسال الفردي
             updateOperationCompletionStatus(operationId, recipients.length, successCount, recipients.length - successCount, results, error.message);

             // إذا لم يتم إرسال استجابة أولية بعد، قم بإرسال استجابة خطأ الآن
              if (!res.headersSent) {
                 res.status(500).json({
                     success: false,
                     error: error.message,
                     operationId: operationId,
                     results: results // قد تكون قائمة فارغة أو جزئية هنا
                 });
             }

        } finally {
              // حذف الملف المؤقت في النهاية
             if (tempFilePath && fs.existsSync(tempFilePath)) {
                 fs.unlink(tempFilePath, (err) => {
            if (err) {
                         console.error(`API /send-image: خطأ في حذف الملف المؤقت ${tempFilePath}:`, err);
                     } else {
                         console.log(`API /send-image: تم حذف الملف المؤقت: ${tempFilePath}`);
                     }
                 });
             } else {
                 console.warn('API /send-image: لا يوجد مسار ملف مؤقت صالح لحذفه في النهاية.');
             }

             // تنظيف ملف بيانات العملية المؤقت بعد فترة
        setTimeout(() => {
            try {
                tempUtils.cleanupConnectionData(operationId);
                     console.log(`API /send-image (الخلفية): تم تنظيف البيانات المؤقتة للعملية ${operationId}`);
            } catch (cleanupError) {
                     console.error('API /send-image (الخلفية): خطأ في تنظيف ملف البيانات المؤقت للعملية:', cleanupError);
            }
        }, 3600000); // حذف الملف بعد ساعة
         }
    })(); // تشغيل الدالة اللامسماة على الفور
});

// API لإرسال ملف
router.post('/send-file/:accountName', upload.single('file'), async (req, res) => {
     let operationId = req.body.operationId || 'op-' + Date.now() + '-' + Math.floor(Math.random() * 1E9);
    let accountName = req.params.accountName;
    const recipients = req.body.recipients ? JSON.parse(req.body.recipients) : [];
    const caption = req.body.caption || '';
    const file = req.file;
    const messageInterval = req.body.messageInterval || 3;

    // إنشاء بيانات العملية الأولية
    const initialOperationData = {
        operationId,
        accountName,
        startTime: Date.now(),
        status: 'processing',
        messageType: 'file',
        recipientsCount: recipients.length,
        success: null,
        successCount: 0,
        failureCount: 0,
        results: []
    };

    // حفظ بيانات العملية في الملف المؤقت
    tempUtils.saveConnectionData(operationId, initialOperationData);
    console.log(`تم إنشاء بيانات العملية ${operationId} بنجاح`);

    console.log(`/api/send-file/:${accountName} - Request received`, { operationId, recipientsCount: recipients.length, captionProvided: !!caption, file: file ? file.originalname : 'none' });

    if (!recipients || recipients.length === 0) {
         console.error(`/api/send-file/:${accountName} - No recipients provided.`);
        // تنظيف الملف المرفوع إذا وجد
        if (file && fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
        }
        return res.status(400).json({ success: false, error: 'لم يتم تحديد مستلم واحد على الأقل.', operationId });
    }

    if (!file) {
         console.error(`/api/send-file/:${accountName} - No file uploaded.`);
        return res.status(400).json({ success: false, error: 'لم يتم تحميل الملف.', operationId });
    }

     // إرسال استجابة أولية فوراً لبدء العملية في الخلفية إذا لم يتم تتبعها بالفعل
     if (req.body.operationId && !res.headersSent) {
        console.log(`/api/send-file/:${accountName} - Sending initial response for operation ${operationId}.`);
         res.json({
             success: true,
             operationId: operationId,
             message: 'تم بدء عملية إرسال الملف',
             status: 'processing',
             totalRecipients: recipients.length
         });
     }


    // تشغيل عملية الإرسال الفعلية في الخلفية
    (async () => {
        let results = [];
        let successCount = 0;
        let failCount = 0;
        let tempFilePath = file.path; // مسار الملف المؤقت


        try {
             // ** إضافة منطق الانتظار وإعادة المحاولة هنا **
             let connectionReady = false;
             const maxRetries = 10; // عدد مرات إعادة المحاولة
             const retryDelay = 2000; // التأخير بين المحاولات بالملي ثانية (2 ثانية)
             let currentRetry = 0;
             let activeConnection = null;

             while (currentRetry < maxRetries && !connectionReady) {
                 try {
                     activeConnection = getActiveConnection(accountName, req.app.get('whatsappConnections'));
                     // إذا وصلت هنا، فالاتصال متاح ومبدئياً جاهز
                     connectionReady = true;
                     console.log(`API /send-file: الاتصال بالحساب ${accountName} جاهز بعد ${currentRetry} محاولة.`);
                 } catch (connError) {
                     console.warn(`API /send-file: محاولة ${currentRetry + 1}/${maxRetries} للحصول على الاتصال بالحساب ${accountName} فشلت: ${connError.message}`);
                     currentRetry++;
                     if (currentRetry < maxRetries) {
                         await new Promise(resolve => setTimeout(resolve, retryDelay));
                     } else {
                         // إذا فشلت جميع المحاولات
                         throw connError; // أعد رمي الخطأ ليتم التقاطه في catch الخارجية
                     }
                 }
             }

             if (!activeConnection || !activeConnection.sock) {
                 throw new Error('فشل في الحصول على اتصال واتساب نشط بعد عدة محاولات.');
             }

             const sock = activeConnection.sock;

            for (let i = 0; i < recipients.length; i++) {
                const recipient = recipients[i];
                let result;
                let detectedType = null;
                try {
                    // فحص نوع الملف الحقيقي
                    const { fileTypeFromFile } = await import('file-type');
                    detectedType = await fileTypeFromFile(tempFilePath);
                    console.log(`نوع الملف الحقيقي:`, detectedType);
                    if (detectedType && detectedType.mime.startsWith('image/')) {
                        result = await sendImage(sock, recipient, tempFilePath, caption, accountName);
                        result.mediaType = 'image';
                    } else {
                        result = await sendFile(sock, recipient, tempFilePath, caption, accountName, detectedType);
                        result.mediaType = detectedType ? detectedType.mime : 'file';
                    }
                    results.push(result);
                    if (result.success) {
                        successCount++;
                        if (operationId) {
                            addMessageResult(operationId, { recipient: recipient.id || recipient.name, success: true, messageId: result.messageId });
                        }
                    } else {
                         failCount++;
                        console.error(`API /send-file: فشل إرسال الملف إلى ${recipient.name}: ${result.error}`);
                        if (operationId) {
                            addMessageResult(operationId, { recipient: recipient.id || recipient.name, success: false, error: result.error });
                        }
                }
            } catch (error) {
                    console.error(`API /send-file: خطأ فردي أثناء إرسال الملف إلى ${recipient.name}:`, error);
                    results.push({
                    success: false,
                        recipient: recipient,
                        error: error.message,
                        mediaType: detectedType ? detectedType.mime : 'file',
                        fileName: file.originalname,
                        caption: caption || null,
                        status: 'failed'
                    });
                    failCount++;
                    if (operationId) {
                        addMessageResult(operationId, { recipient: recipient.id || recipient.name, success: false, error: error.message });
                    }
                }
                 if (i < recipients.length - 1) {
                     console.log(`API /send-file: انتظار ${messageInterval} ثوانٍ قبل إرسال الرسالة التالية...`);
                    await new Promise(resolve => setTimeout(resolve, messageInterval * 1000));
                }
            }

             console.log(`/api/send-file/:${accountName} - Batch sending complete. Success: ${successCount}, Failed: ${failCount}`);

             // تسجيل النتيجة النهائية للعملية باستخدام operationId
             updateOperationCompletionStatus(operationId, recipients.length, successCount, failCount, results);


    } catch (error) {
             console.error(`/api/send-file/:${accountName} - خطأ عام في معالجة الإرسال:`, error);

             // في حالة حدوث خطأ قبل البدء بالإرسال الفردي
             updateOperationCompletionStatus(operationId, recipients.length, successCount, recipients.length - successCount, results, error.message);

             // إذا لم يتم إرسال استجابة أولية بعد، قم بإرسال استجابة خطأ الآن
              if (!res.headersSent) {
                 res.status(500).json({
                     success: false,
                    error: error.message,
                     operationId: operationId,
                     results: results // قد تكون قائمة فارغة أو جزئية هنا
                 });
             }

        } finally {
              // حذف الملف المؤقت في النهاية
             if (tempFilePath && fs.existsSync(tempFilePath)) {
                 fs.unlink(tempFilePath, (err) => {
                     if (err) {
                         console.error(`API /send-file: خطأ في حذف الملف المؤقت ${tempFilePath}:`, err);
                          // يمكنك تسجيل هذا الخطأ في بيانات العملية إذا كنت تتبعها
                     } else {
                         console.log(`API /send-file: تم حذف الملف المؤقت: ${tempFilePath}`);
                     }
                 });
             }
        }
    })(); // تشغيل الدالة اللامسماة على الفور
});

module.exports = router;
