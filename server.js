const express = require('express');
const http = require('http');
const path = require('path');
const fs = require('fs');
const qrcode = require('qrcode');
const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const bodyParser = require('body-parser');
const cors = require('cors');
const { saveAccountData, loadAccountData, saveContactsData, loadContactsData, getContactsLastUpdated } = require('./utils');
const operationRouter = require('./operation-api'); // استيراد موجه API العمليات
const tempUtils = require('./temp-utils'); // استيراد وظائف إدارة الملفات المؤقتة

// تخزين اتصالات Server-Sent Events للعملاء
const sseClients = new Map(); // Map<accountName, Set<response objects>>

// دالة لإرسال تحديثات حالة الرسائل لجميع العملاء المتصلين
function broadcastMessageStatusUpdate(accountName, statusUpdate) {
    const clients = sseClients.get(accountName);
    if (clients && clients.size > 0) {
        const data = JSON.stringify(statusUpdate);
        console.log(`Broadcasting message status update for ${accountName}:`, data);

        // إرسال التحديث لجميع العملاء المتصلين
        clients.forEach(client => {
            try {
                client.write(`event: messageStatusUpdate\ndata: ${data}\n\n`);
            } catch (error) {
                console.error('Error sending SSE message:', error);
                // إزالة العميل المعطوب
                clients.delete(client);
            }
        });
    }
}

const app = express();
app.use(bodyParser.json());
app.use(cors());

// استيراد وتسجيل موجه API الرسائل
const messageApiRouter = require('./message-api');
const { getRecipientInfoByMessageId } = require('./message-api');
app.use('/api', messageApiRouter);

// إضافة router لـ API العمليات
app.use('/api', operationRouter);

// ميدلوير للتشخيص وتتبع الطلبات المتعلقة بالعمليات
app.use('/api/operation', (req, res, next) => {
    console.log(`[${new Date().toISOString()}] طلب للعملية: ${req.path} - بطريقة: ${req.method}`);
    next();
});

// ميدلوير للتشخيص وتتبع الطلبات المتعلقة بإرسال الرسائل
app.use('/api/send-', (req, res, next) => {
    console.log(`[${new Date().toISOString()}] طلب إرسال رسالة: ${req.path} - بطريقة: ${req.method}`);
    next();
});

// كائن لتخزين حالة اتصالات واتساب
const connections = {}; // سيحتوي الآن على { client, qr, pairingCode, status, info, phoneNumber }

// تخزين كائن connections في app لاستخدامه في API
app.set('whatsappConnections', connections);

// استضافة الملفات الثابتة
app.use(express.static(path.join(__dirname, 'public')));

const PORT = process.env.PORT || 3045;

// إضافة مهمة دورية لتنظيف الملفات المؤقتة القديمة
setInterval(() => {
    try {
        const tempUtils = require('./temp-utils');
        const cleanedCount = tempUtils.cleanupOldTempFiles();
        if (cleanedCount > 0) {
            console.log(`تم تنظيف ${cleanedCount} ملفات مؤقتة قديمة بواسطة المهمة الدورية`);
        }
    } catch (error) {
        console.error('Error in scheduled cleanup task:', error);
    }
}, 3600000); // تنفيذ المهمة كل ساعة

// إضافة مسار لصفحة تفاصيل الحساب
app.get('/account-details.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'account-details.html'));
});

// إضافة مسار لصفحة إرسال الرسائل
app.get('/send-message.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'send-message.html'));
});

// نقطة نهاية Server-Sent Events لتحديثات حالة الرسائل
app.get('/api/message-status-updates/:accountName', (req, res) => {
    const { accountName } = req.params;

    console.log(`SSE connection established for account: ${accountName}`);

    // إعداد headers لـ SSE
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

    // إرسال رسالة اتصال أولية
    res.write(`event: connected\ndata: ${JSON.stringify({ message: 'Connected to message status updates', accountName })}\n\n`);

    // إضافة العميل إلى قائمة العملاء المتصلين
    if (!sseClients.has(accountName)) {
        sseClients.set(accountName, new Set());
    }
    sseClients.get(accountName).add(res);

    // التعامل مع إغلاق الاتصال
    req.on('close', () => {
        console.log(`SSE connection closed for account: ${accountName}`);
        const clients = sseClients.get(accountName);
        if (clients) {
            clients.delete(res);
            if (clients.size === 0) {
                sseClients.delete(accountName);
            }
        }
    });

    // إرسال heartbeat كل 30 ثانية للحفاظ على الاتصال
    const heartbeat = setInterval(() => {
        try {
            res.write(`event: heartbeat\ndata: ${JSON.stringify({ timestamp: Date.now() })}\n\n`);
        } catch (error) {
            console.error('Error sending heartbeat:', error);
            clearInterval(heartbeat);
            const clients = sseClients.get(accountName);
            if (clients) {
                clients.delete(res);
            }
        }
    }, 30000);

    // تنظيف heartbeat عند إغلاق الاتصال
    req.on('close', () => {
        clearInterval(heartbeat);
    });
});

// وظيفة لإنشاء اتصال واتساب باستخدام whatsapp-web.js
async function createWhatsAppConnection(accountName, phoneNumberForPairing = null) {
    console.log(`Attempting to create WhatsApp connection for: ${accountName}`);

    // التحقق من وجود اتصال سابق
    if (connections[accountName] && connections[accountName].client) {
        console.log(`Connection already exists for ${accountName}, checking status...`);
        const status = connections[accountName].status;
        if (status === 'connected' || status === 'authenticated') {
            console.log(`Active connection found for ${accountName}, reusing...`);
            return connections[accountName];
        }
    }

    try {
        // إنشاء مجلد جلسة إذا لم يكن موجوداً
        const sessionDir = `./sessions/${accountName}`;
        if (!fs.existsSync(sessionDir)) {
            fs.mkdirSync(sessionDir, { recursive: true });
        }

        // حفظ معلومات الحساب الأولية في JSON إذا لم تكن موجودة بالفعل
        const savedAccountInfo = loadAccountData(accountName);
        if (!savedAccountInfo) {
            const initialAccountInfo = {
                name: accountName,
                number: phoneNumberForPairing || '',
                status: 'initializing',
                createdAt: new Date().toISOString()
            };
            saveAccountData(accountName, initialAccountInfo);
            console.log(`Initial account data saved for ${accountName}`);
        }

        // مسار مجلد الجلسة لـ LocalAuth
        const wwebjsSessionPath = path.join(__dirname, '.wwebjs_auth', `session-${accountName}`);
        console.log(`Session path for ${accountName}: ${wwebjsSessionPath}`);

        const client = new Client({
            authStrategy: new LocalAuth({ clientId: accountName, dataPath: path.join(__dirname, '.wwebjs_auth') }),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ],
            },
        });

        // تخزين معلومات الاتصال الأولية
        connections[accountName] = {
            client,
            sock: client, // إضافة sock كمرجع للعميل
            phoneNumber: phoneNumberForPairing,
            qr: '',
            pairingCode: '',
            status: 'initializing',
            info: loadAccountData(accountName),
            lastConnect: new Date().toISOString()
        };

        client.on('qr', async (qr) => {
            console.log(`QR RECEIVED for ${accountName}`);
            // إذا كنا في وضع طلب رمز الاقتران، لا نعتمد على QR بنفس الطريقة
            if (!connections[accountName].pairingCode) {
                try {
                connections[accountName].qr = await qrcode.toDataURL(qr);
                connections[accountName].status = 'qr_ready';
                // تحديث حالة الحساب في ملف JSON
                const accountInfo = loadAccountData(accountName);
                if (accountInfo) {
                    accountInfo.status = 'qr_ready';
                    saveAccountData(accountName, accountInfo);
                }
                } catch (qrErr) {
                    console.error(`Error generating QR for ${accountName}:`, qrErr);
                    connections[accountName].status = 'qr_error';
                    // تحديث حالة الحساب في ملف JSON
                    const accountInfo = loadAccountData(accountName);
                    if (accountInfo) {
                        accountInfo.status = 'qr_error';
                        saveAccountData(accountName, accountInfo);
                    }
                }
            }
        });

        client.on('ready', async () => {
            console.log(`Client is ready for ${accountName}!`);

            // تحديث حالة الاتصال
            if (connections[accountName]) {
                connections[accountName].status = 'connected';
                connections[accountName].qr = '';
                connections[accountName].pairingCode = '';
                connections[accountName].sock = client;
                connections[accountName].lastConnect = new Date().toISOString();

                try {
                    const clientInfo = client.info;
                    const profilePicUrl = await client.getProfilePicUrl(clientInfo.wid._serialized).catch(() => null);

                    // تحديث معلومات الحساب
                    const accountInfo = {
                        name: clientInfo.pushname || 'غير معروف',
                        number: convertCusToRealPhone(clientInfo.wid._serialized),
                        profilePictureUrl: profilePicUrl,
                        status: 'connected',
                        jid: clientInfo.wid._serialized,
                        id: clientInfo.wid.user,
                        lastConnected: new Date().toISOString()
                    };

                    connections[accountName].info = accountInfo;
                    saveAccountData(accountName, accountInfo);

                    console.log(`Connection updated and ready for ${accountName}`);
                } catch (error) {
                    console.error(`Error updating connection info for ${accountName}:`, error);
                }
            } else {
                console.error(`Connection object not found for ${accountName} in ready event`);
            }
        });

        client.on('authenticated', (session) => {
            console.log(`AUTHENTICATED for ${accountName}`);
            connections[accountName].status = 'authenticated';
            // تحديث الحالة في ملف JSON
            const accountInfo = loadAccountData(accountName);
            if (accountInfo) {
                accountInfo.status = 'authenticated';
                accountInfo.lastAuthenticated = new Date().toISOString();
                saveAccountData(accountName, accountInfo);
            }
        });

        client.on('auth_failure', msg => {
            console.error(`AUTHENTICATION FAILURE for ${accountName}:`, msg);
            connections[accountName].status = 'auth_failure';
            // تحديث الحالة في ملف JSON
            const accountInfo = loadAccountData(accountName);
            if (accountInfo) {
                accountInfo.status = 'auth_failure';
                accountInfo.lastAuthFailure = new Date().toISOString();
                accountInfo.authFailureMessage = msg;
                saveAccountData(accountName, accountInfo);
            }
        });

        // إضافة استماع لأحداث message_ack لتتبع حالة الرسائل
        client.on('message_ack', (message, ack) => {
            console.log(`Message ACK received for ${accountName}:`, {
                messageId: message.id.id,
                ack: ack,
                from: message.from,
                to: message.to
            });

            // تحويل رقم ACK إلى نص
            let status = 'unknown';
            switch (ack) {
                case -1:
                    status = 'failed';
                    break;
                case 0:
                    status = 'pending';
                    break;
                case 1:
                    status = 'sent';
                    break;
                case 2:
                    status = 'delivered';
                    break;
                case 3:
                    status = 'read';
                    break;
                case 4:
                    status = 'played';
                    break;
            }

            // البحث عن معلومات المستلم من معرف الرسالة
            const recipientInfo = getRecipientInfoByMessageId(message.id.id);

            // إرسال تحديث الحالة لجميع العملاء المتصلين
            broadcastMessageStatusUpdate(accountName, {
                messageId: message.id.id,
                status: status,
                ack: ack,
                timestamp: Date.now(),
                recipient: message.to,
                operationId: recipientInfo?.operationId,
                recipientIndex: recipientInfo?.recipientInfo?.recipientIndex,
                messageType: recipientInfo?.recipientInfo?.messageType
            });
        });

        client.on('disconnected', (reason) => {
            console.log(`Client was logged out for ${accountName}:`, reason);
            connections[accountName].status = 'disconnected';
            connections[accountName].sock = null; // مسح sock عند قطع الاتصال
            // تحديث الحالة في ملف JSON
            const accountInfo = loadAccountData(accountName);
            if (accountInfo) {
                accountInfo.status = 'disconnected';
                accountInfo.lastDisconnected = new Date().toISOString();
                accountInfo.disconnectReason = reason;
                saveAccountData(accountName, accountInfo);
            }
        });

        console.log(`Initializing client for ${accountName}...`);
        await client.initialize(); // التهيئة ستبدأ عملية الـ QR أو تنتظر رمز الاقتران إذا طُلب

        // إذا تم توفير رقم هاتف لرمز الاقتران، قم بطلبه الآن بعد تهيئة العميل
        if (phoneNumberForPairing && client) {
            try {
                console.log(`Requesting pairing code for ${accountName} with phone ${phoneNumberForPairing}`);
                // تنظيف رقم الهاتف قبل إرساله
                const cleanedPhoneNumber = phoneNumberForPairing.replace(/[^\d]/g, '');
                const code = await client.requestPairingCode(cleanedPhoneNumber);
                                    if (code) {
                    console.log(`Pairing code for ${accountName} (${cleanedPhoneNumber}): ${code}`);
                    connections[accountName].pairingCode = code;
                    connections[accountName].status = 'pairing_code_ready';
                    // تحديث الحالة في ملف JSON
                    const accountInfo = loadAccountData(accountName);
                    if (accountInfo) {
                        accountInfo.status = 'pairing_code_ready';
                        accountInfo.pairingCodeRequested = new Date().toISOString();
                        saveAccountData(accountName, accountInfo);
                    }
                                    } else {
                    console.error(`Failed to get pairing code for ${accountName}.`);
                    connections[accountName].status = 'pairing_code_error';
                    // تحديث الحالة في ملف JSON
                    const accountInfo = loadAccountData(accountName);
                    if (accountInfo) {
                        accountInfo.status = 'pairing_code_error';
                        accountInfo.pairingCodeError = new Date().toISOString();
                        saveAccountData(accountName, accountInfo);
                    }
                }
            } catch (pairError) {
                console.error(`Error requesting pairing code for ${accountName}:`, pairError);
                connections[accountName].status = 'pairing_code_error';
                // تحديث الحالة في ملف JSON
                const accountInfo = loadAccountData(accountName);
                if (accountInfo) {
                    accountInfo.status = 'pairing_code_error';
                    accountInfo.pairingCodeError = new Date().toISOString();
                    accountInfo.pairingCodeErrorMessage = pairError.message;
                    saveAccountData(accountName, accountInfo);
                }
                                    }
                                } else {
             console.log(`Client initialized for ${accountName}. Waiting for QR or ready state if pairing code was not requested.`);
                                }

        return true;
                            } catch (error) {
        console.error(`Error creating WhatsApp connection for ${accountName}:`, error);
        if (connections[accountName]) {
            connections[accountName].status = 'error';
        }

        // تحديث حالة الخطأ في ملف JSON
        const accountInfo = loadAccountData(accountName);
        if (accountInfo) {
            accountInfo.status = 'error';
            accountInfo.lastError = new Date().toISOString();
            accountInfo.errorMessage = error.message;
            saveAccountData(accountName, accountInfo);
        }

        return false;
    }
}

// API لإنشاء اتصال جديد
app.post('/api/create-connection', async (req, res) => {
    try {
        const { accountName, phoneNumber, connectionType } = req.body;

        if (!accountName) {
            return res.status(400).json({ error: 'اسم الحساب مطلوب' });
        }

        // إنشاء مجلد الجلسات إذا لم يكن موجوداً
        const sessionDir = `./sessions/${accountName}`;
        if (!fs.existsSync(sessionDir)) {
            fs.mkdirSync(sessionDir, { recursive: true });
        }

        // التحقق مما إذا كان الحساب موجوداً بالفعل في الاتصالات النشطة
        if (connections[accountName] &&
            connections[accountName].status !== 'disconnected' &&
            connections[accountName].status !== 'error' &&
            connections[accountName].status !== 'auth_failure' &&
            connections[accountName].status !== 'pairing_code_error') {
            return res.status(400).json({ error: 'الحساب موجود بالفعل أو قيد المعالجة' });
        }

        // حفظ بيانات الحساب الأولية
        const savedAccountInfo = loadAccountData(accountName);
        if (!savedAccountInfo) {
            // إنشاء معلومات الحساب الأولية
            const initialAccountInfo = {
                name: accountName,
                number: phoneNumber || '',
                status: 'created',
                createdAt: new Date().toISOString(),
                connectionType: connectionType || 'qr' // حفظ نوع الاتصال الذي اختاره المستخدم
            };
            saveAccountData(accountName, initialAccountInfo);
            console.log(`Initial account data created and saved for ${accountName}`);
        } else {
            // تحديث البيانات الموجودة
            savedAccountInfo.number = phoneNumber || savedAccountInfo.number;
            savedAccountInfo.status = 'updating';
            savedAccountInfo.connectionType = connectionType || 'qr';
            savedAccountInfo.lastUpdated = new Date().toISOString();
            saveAccountData(accountName, savedAccountInfo);
            console.log(`Updated existing account data for ${accountName}`);
        }

        // إذا كان المستخدم قد اختار الاتصال برمز الاقتران وتم توفير رقم هاتف
        if (connectionType === 'pairing' && phoneNumber) {
            // إنشاء اتصال جديد مع رقم الهاتف لطلب رمز الاقتران
            const success = await createWhatsAppConnection(accountName, phoneNumber);

            if (success) {
                // انتظار قليلاً للتأكد من تهيئة العميل
                await new Promise(resolve => setTimeout(resolve, 3000));

                // طلب رمز الاقتران
                const result = await requestPairingCodeForAccount(accountName, phoneNumber);

                if (result.success) {
                    return res.json({
                        success: true,
                        accountName,
                        connectionType: 'pairing',
                        pairingCode: result.code,
                        status: connections[accountName].status,
                        message: `رمز الاقتران لـ ${accountName} هو: ${result.code}`
                    });
                } else {
                    return res.status(500).json({
                        error: `فشل في طلب رمز الاقتران: ${result.error}`,
                        connectionType: 'pairing'
                    });
                }
            } else {
                return res.status(500).json({ error: 'حدث خطأ أثناء تهيئة الاتصال لطلب رمز الاقتران' });
            }
        } else {
            // إنشاء اتصال جديد باستخدام QR (الطريقة الافتراضية)
            const success = await createWhatsAppConnection(accountName, null);

            if (success) {
                return res.status(200).json({
                    success: true,
                    accountName,
                    connectionType: 'qr',
                    message: "تم إنشاء الحساب بنجاح. جاري تهيئة الاتصال، يرجى التحقق من رمز QR."
                });
            } else {
                return res.status(500).json({ error: 'حدث خطأ أثناء تهيئة الاتصال الأولي' });
            }
        }
    } catch (error) {
        console.error('Error in /api/create-connection:', error);
        return res.status(500).json({ error: 'حدث خطأ أثناء إنشاء الاتصال' });
    }
});

// تعديل دالة requestPairingCodeForAccount في ملف server.js
async function requestPairingCodeForAccount(accountName, phoneNumber) {
    try {
        if (!connections[accountName] || !connections[accountName].client) {
            throw new Error('عميل واتساب غير مهيأ');
        }

        // تنظيف رقم الهاتف
        const cleanedPhoneNumber = phoneNumber.replace(/[^\d]/g, '');

        console.log(`طلب رمز اقتران للحساب ${accountName} مع رقم الهاتف ${cleanedPhoneNumber}`);

        // استخدام معلمة واحدة فقط لتجنب مشكلة التعريف في TypeScript
        const code = await connections[accountName].client.requestPairingCode(cleanedPhoneNumber);

        if (!code) {
            throw new Error('فشل في الحصول على رمز الاقتران (رمز فارغ)');
        }

        // تحديث حالة الاتصال
        connections[accountName].pairingCode = code;
        connections[accountName].status = 'pairing_code_ready';
        connections[accountName].phoneNumber = cleanedPhoneNumber;

        console.log(`تم الحصول على رمز الاقتران بنجاح للحساب ${accountName}: ${code}`);

        return { success: true, code, accountName };
    } catch (error) {
        console.error(`خطأ في طلب رمز الاقتران للحساب ${accountName}:`, error);
        return { success: false, error: error.message, accountName };
    }
}

// تحديث مسار الحصول على رمز الاقتران لجعله أكثر بساطة
app.get('/api/get-pairing-code/:accountName', async (req, res) => {
    try {
        const { accountName } = req.params;

        // التحقق من وجود الحساب
        let savedAccountInfo = loadAccountData(accountName);

        if (!savedAccountInfo) {
            const phoneNumber = req.query.phoneNumber;
            if (!phoneNumber) {
                return res.status(400).json({
                    error: 'لم يتم العثور على حساب بهذا الاسم. يرجى توفير رقم الهاتف لإنشاء حساب جديد.'
                });
            }

            // إنشاء بيانات الحساب قبل محاولة الاتصال
            savedAccountInfo = {
                name: accountName,
                number: phoneNumber,
                status: 'created',
                createdAt: new Date().toISOString()
            };

            // تأكد من وجود مجلد الجلسة
            const sessionDir = `./sessions/${accountName}`;
            if (!fs.existsSync(sessionDir)) {
                fs.mkdirSync(sessionDir, { recursive: true });
            }

            saveAccountData(accountName, savedAccountInfo);
            console.log(`Created new account data for ${accountName} with phone ${phoneNumber} from get-pairing-code`);
        }

        if (!connections[accountName]) {
            // استخدام رقم الهاتف المحفوظ أو المقدم في الطلب
            const phoneNumber = savedAccountInfo?.number || req.query.phoneNumber;

            if (!phoneNumber) {
                return res.status(400).json({
                    error: 'لم يتم العثور على رقم هاتف محفوظ لهذا الحساب. يرجى توفير رقم الهاتف عبر المعلمة phoneNumber'
                });
            }

            // إنشاء اتصال جديد باستخدام رقم الهاتف
            const success = await createWhatsAppConnection(accountName, phoneNumber);

            if (!success) {
                return res.status(500).json({
                    error: 'فشل في إنشاء اتصال جديد للحساب. يرجى مراجعة السجلات.'
                });
            }

            // انتظار قليلاً للتأكد من تهيئة العميل
            await new Promise(resolve => setTimeout(resolve, 3000));
        }

        // محاولة طلب رمز الاقتران
        const phoneNumber = connections[accountName]?.phoneNumber ||
                           savedAccountInfo?.number ||
                           req.query.phoneNumber;

        if (!phoneNumber) {
            return res.status(400).json({
                error: 'لم يتم العثور على رقم هاتف محفوظ لهذا الحساب. يرجى توفير رقم الهاتف عبر المعلمة phoneNumber'
            });
        }

        const result = await requestPairingCodeForAccount(accountName, phoneNumber);

        if (result.success) {
            return res.json({
                success: true,
                pairingCode: result.code,
                status: connections[accountName].status,
                accountName: accountName,
                message: `رمز الاقتران لـ ${accountName} هو: ${result.code}`
            });
        } else {
            return res.status(500).json({
                error: `فشل في طلب رمز الاقتران: ${result.error}`
            });
        }
    } catch (error) {
        console.error('Error in /api/get-pairing-code:', error);
        return res.status(500).json({ error: 'حدث خطأ داخلي أثناء طلب رمز الاقتران' });
    }
});

// تحسين مسار طلب رمز الاقتران مع الهاتف
app.post('/api/request-pairing-code/:accountName', async (req, res) => {
    try {
        const { accountName } = req.params;
        const { phoneNumber } = req.body;

        if (!accountName) {
            return res.status(400).json({ error: 'اسم الحساب مطلوب' });
        }
        if (!phoneNumber) {
            return res.status(400).json({ error: 'رقم الهاتف مطلوب لطلب رمز الاقتران' });
        }

        // التحقق من وجود الحساب
        let savedAccountInfo = loadAccountData(accountName);

        // إذا لم يكن الحساب موجوداً، قم بإنشائه أولاً
        if (!savedAccountInfo) {
            // إنشاء معلومات الحساب الأولية
            savedAccountInfo = {
                name: accountName,
                number: phoneNumber,
                status: 'created',
                createdAt: new Date().toISOString(),
                connectionType: 'pairing'
            };
            // تأكد من وجود مجلد الجلسة
            const sessionDir = `./sessions/${accountName}`;
            if (!fs.existsSync(sessionDir)) {
                fs.mkdirSync(sessionDir, { recursive: true });
            }
            saveAccountData(accountName, savedAccountInfo);
            console.log(`Created new account data for ${accountName} with phone ${phoneNumber}`);
        } else {
            // تحديث رقم الهاتف إذا تم توفيره
            savedAccountInfo.number = phoneNumber;
            savedAccountInfo.connectionType = 'pairing';
            savedAccountInfo.lastUpdated = new Date().toISOString();
            saveAccountData(accountName, savedAccountInfo);
            console.log(`Updated phone number for ${accountName} to ${phoneNumber}`);
        }

        // التحقق مما إذا كان الاتصال موجودًا بالفعل
        if (connections[accountName] &&
            (connections[accountName].status === 'connected' || connections[accountName].status === 'authenticated')) {
            return res.status(400).json({
                error: 'الحساب متصل بالفعل. يجب قطع الاتصال أولاً إذا كنت تريد طلب رمز اقتران جديد.',
                connectionType: 'pairing'
            });
        }

        // إذا كان هناك اتصال سابق في حالة خطأ، قم بحذفه
        if (connections[accountName] &&
            (connections[accountName].status === 'error' ||
             connections[accountName].status === 'auth_failure' ||
             connections[accountName].status === 'pairing_code_error' ||
             connections[accountName].status === 'disconnected')) {
            try {
                if (connections[accountName].client) {
                    await connections[accountName].client.destroy().catch(e => console.warn(`Destroy error: ${e.message}`));
                }
                delete connections[accountName];
            } catch (e) {
                console.warn(`Failed to clean up previous connection: ${e.message}`);
            }
        }

        // إنشاء اتصال جديد مع رقم الهاتف
        const success = await createWhatsAppConnection(accountName, phoneNumber);

        if (!success) {
            return res.status(500).json({
                error: 'فشل في إنشاء اتصال جديد للحساب. يرجى مراجعة السجلات.',
                connectionType: 'pairing'
            });
        }

        // انتظار قليلاً للتأكد من تهيئة العميل
        await new Promise(resolve => setTimeout(resolve, 3000));

        // طلب رمز الاقتران
        const result = await requestPairingCodeForAccount(accountName, phoneNumber);

        if (result.success) {
            return res.json({
                success: true,
                pairingCode: result.code,
                status: connections[accountName].status,
                accountName: accountName,
                connectionType: 'pairing',
                message: `رمز الاقتران لـ ${accountName} هو: ${result.code}`
            });
        } else {
            return res.status(500).json({
                error: `فشل في طلب رمز الاقتران: ${result.error}`,
                connectionType: 'pairing'
            });
        }
    } catch (error) {
        console.error(`Error in /api/request-pairing-code/${req.params.accountName}:`, error);
        return res.status(500).json({
            error: `حدث خطأ أثناء طلب رمز الاقتران: ${error.message}`,
            connectionType: 'pairing'
        });
    }
});

// API لإعادة الاتصال بحساب موجود
app.post('/api/reconnect/:accountName', async (req, res) => {
    try {
        const { accountName } = req.params;
        const sessionDir = path.join(__dirname, '.wwebjs_auth', `session-${accountName}`);

        if (!fs.existsSync(sessionDir)) {
             // إذا لم يكن هناك مجلد جلسة، فهذا يعني أنه اتصال جديد
            console.log(`No session folder found for ${accountName}, attempting to create a new connection.`);
            // يمكنك اختيار الحصول على phoneNumber من req.body إذا لزم الأمر
            const phoneNumber = req.body.phoneNumber || (connections[accountName]?.phoneNumber || '');
            await createWhatsAppConnection(accountName, phoneNumber);
            return res.status(200).json({
                success: true,
                message: `بدء اتصال جديد لـ ${accountName}. يرجى التحقق من QR.`,
                accountName
            });
        }

        // إذا كان الاتصال موجودًا بالفعل ويحاول إعادة الاتصال
        if (connections[accountName] && connections[accountName].client) {
            console.log(`Attempting to re-initialize existing client for ${accountName}`);
            try {
                await connections[accountName].client.initialize();
                 return res.status(200).json({
                    success: true,
                    message: `تمت محاولة إعادة تهيئة الاتصال لـ ${accountName}.`,
                    accountName
                });
            } catch (initError) {
                console.error(`Error re-initializing client for ${accountName}:`, initError);
                // إذا فشلت إعادة التهيئة، قد نرغب في محاولة إنشاء اتصال جديد بالكامل
                delete connections[accountName]; // إزالة القديم
                const phoneNumber = req.body.phoneNumber || '';
                await createWhatsAppConnection(accountName, phoneNumber);
                return res.status(200).json({
                    success: true,
                    message: `فشلت إعادة التهيئة، بدء اتصال جديد لـ ${accountName}. يرجى التحقق من QR.`,
                    accountName
                });
            }
        } else {
            // إذا لم يكن هناك كائن اتصال، أنشئ واحدًا جديدًا (سيستخدم LocalAuth الجلسة المحفوظة إذا وجدت)
            console.log(`No active client object for ${accountName}, creating new connection (will use saved session if available).`);
            const phoneNumber = req.body.phoneNumber || '';
            await createWhatsAppConnection(accountName, phoneNumber);
        return res.status(200).json({
            success: true,
                message: `تمت محاولة الاتصال بالحساب ${accountName} باستخدام الجلسة المحفوظة إن وجدت.`,
                accountName
        });
        }

    } catch (error) {
        console.error('Error reconnecting:', error);
        return res.status(500).json({ error: 'حدث خطأ أثناء إعادة الاتصال' });
    }
});

// API للحصول على QR code (لم تعد هناك حاجة لرمز الاقتران بنفس الطريقة)
app.get('/api/get-qr/:accountName', (req, res) => {
    const { accountName } = req.params;

    if (!connections[accountName]) {
        return res.status(404).json({ error: 'الحساب غير موجود أو لم تبدأ عملية الاتصال بعد' });
    }

    const conn = connections[accountName];

    // التحقق أولاً من رمز الاقتران إذا كان متاحًا
    if (conn.status === 'pairing_code_ready' && conn.pairingCode) {
        return res.json({
            pairingCode: conn.pairingCode,
            qr: '', // لا يوجد QR في هذه الحالة
            status: conn.status,
            message: `أدخل رمز الاقتران هذا في هاتفك: ${conn.pairingCode}`
        });
    }
    // التحقق مما إذا كان QR لا يزال متاحًا (لم يتم الاتصال بعد ولم يتم طلب رمز اقتران)
    else if (conn.status === 'qr_ready' && conn.qr) {
        return res.json({
            qr: conn.qr,
            pairingCode: '',
            status: conn.status,
            message: 'امسح رمز QR هذا بهاتفك.'
        });
    } else if (conn.status === 'connected' || conn.status === 'authenticated') {
         return res.json({
            qr: '',
            pairingCode: '',
            status: conn.status,
            message: 'Client is already connected or authenticated.'
        });
    } else {
        // حالات أخرى مثل initializing, error, auth_failure, pairing_code_error
        return res.json({
            qr: conn.qr || '',
            pairingCode: conn.pairingCode || '',
            status: conn.status,
            message: 'Client in a different state or error occurred. Check status for details.'
        });
    }
});

// API للحصول على حالة الاتصال
app.get('/api/connection-status/:accountName', (req, res) => {
    const { accountName } = req.params;

    try {
        if (!connections[accountName]) {
            // التحقق من وجود مجلد الجلسة
            const sessionDir = `./sessions/${accountName}`;
            if (fs.existsSync(sessionDir)) {
                // محاولة قراءة معلومات الحساب من ملف JSON
                const savedAccountInfo = loadAccountData(accountName);

                return res.json({
                    exists: true,
                    connected: false,
                    status: 'disconnected',
                    info: savedAccountInfo,
                    message: 'الحساب موجود ولكنه غير متصل حاليًا'
                });
            }
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        return res.json({
            exists: true,
            connected: connections[accountName].status === 'connected',
            status: connections[accountName].status,
            info: connections[accountName].info
        });
    } catch (error) {
        console.error(`Error checking connection status for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء التحقق من حالة الاتصال' });
    }
});

// API لتحميل الحسابات المتاحة من مجلدات الجلسات
app.get('/api/available-accounts', (req, res) => {
    try {
        // قراءة مجلدات الجلسات
        const sessionDirs = fs.readdirSync('./sessions');

        // تجميع معلومات الحسابات
        const accounts = sessionDirs.map(dir => {
            const isConnected = !!connections[dir];
            let accountInfo = null;
            let phoneNumber = '';

            // محاولة قراءة معلومات الحساب من ملف JSON
            const savedAccountInfo = loadAccountData(dir);

            if (savedAccountInfo) {
                // استخدام المعلومات المحفوظة في ملف JSON
                accountInfo = savedAccountInfo;
                phoneNumber = savedAccountInfo.number || '';
            } else {
                // محاولة قراءة رقم الهاتف من الملف القديم إذا لم يكن هناك ملف JSON
                const phoneNumberPath = path.join('./sessions', dir, 'phone_number.txt');
                if (fs.existsSync(phoneNumberPath)) {
                    try {
                        phoneNumber = fs.readFileSync(phoneNumberPath, 'utf8');
                    } catch (err) {
                        console.error(`Error reading phone number for ${dir}:`, err);
                    }
                }
            }

            return {
                accountName: dir,
                connected: isConnected,
                status: isConnected ? connections[dir].status : (accountInfo ? accountInfo.status || 'disconnected' : 'disconnected'),
                info: isConnected ? connections[dir].info : accountInfo,
                phoneNumber: isConnected ? connections[dir].phoneNumber : phoneNumber
            };
        });

        return res.json({ accounts });
    } catch (error) {
        console.error('Error loading available accounts:', error);
        return res.status(500).json({ error: 'حدث خطأ أثناء تحميل الحسابات المتاحة' });
    }
});

// API للحصول على معلومات الحساب
app.get('/api/account-info/:accountName', (req, res) => {
    const { accountName } = req.params;

    try {
        // التحقق من وجود الحساب في الاتصالات النشطة
        if (connections[accountName]) {
            return res.json({ info: connections[accountName].info });
        }

        // إذا لم يكن الحساب متصلاً، حاول قراءة المعلومات من ملف JSON
        const savedAccountInfo = loadAccountData(accountName);
        if (savedAccountInfo) {
            return res.json({ info: savedAccountInfo });
        }

        // إذا لم يتم العثور على معلومات الحساب
        return res.status(404).json({ error: 'الحساب غير موجود أو لا توجد معلومات متاحة' });
    } catch (error) {
        console.error(`Error fetching account info for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء جلب معلومات الحساب' });
    }
});

// API للحصول على تفاصيل الحساب
app.get('/api/account-details/:accountName', (req, res) => {
    const { accountName } = req.params;

    try {
        // التحقق من وجود الحساب
        if (!connections[accountName] && !fs.existsSync(`./sessions/${accountName}`)) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // الحصول على معلومات الحساب من الاتصال النشط أو من ملف JSON
        let accountInfo = null;
        let connected = false;
        let status = 'disconnected';

        if (connections[accountName]) {
            accountInfo = connections[accountName].info;
            connected = connections[accountName].status === 'connected';
            status = connections[accountName].status;
        } else {
            // محاولة قراءة المعلومات من ملف JSON
            accountInfo = loadAccountData(accountName);
        }

        if (!accountInfo) {
            return res.status(404).json({ error: 'لا توجد معلومات متاحة للحساب' });
        }

        // إعداد البيانات للإرجاع
        const responseData = {
            name: accountInfo.name || 'غير معروف',
            verifiedName: accountInfo.verifiedName || '',
            id: accountInfo.id || accountInfo.jid || '',
            lid: accountInfo.lid || null, // إرجاع معرف LID إذا كان موجودًا
            number: accountInfo.number || '',
            profilePictureUrl: accountInfo.profilePictureUrl || null,
            status: 'connected',
            jid: accountInfo.jid || '',
            user: accountInfo.user || {
                server: '',
                user: '',
                _serialized: ''
            },
            platform: accountInfo.platform || '',
            connected,
            status
        };

        return res.json(responseData);
    } catch (error) {
        console.error(`Error fetching account details for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء جلب تفاصيل الحساب' });
    }
});

// API لاستخراج بيانات المجموعات من ملف groups_data.json
app.get('/api/extract-groups-data/:accountName', async (req, res) => {
    const { accountName } = req.params;

    try {
        // التحقق من وجود الحساب
        if (!connections[accountName]) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // التحقق من اتصال الحساب
        if (connections[accountName].status !== 'connected') {
            return res.status(400).json({ error: 'الحساب غير متصل' });
        }

        const client = connections[accountName].client;

        // الحصول على معرف المستخدم الحالي
        const currentUserWid = client.info.wid._serialized;
        console.log(`استخراج بيانات المجموعات للحساب ${accountName}`);
        console.log(`معرف المستخدم الحالي: ${currentUserWid}`);

        // الحصول على جميع المحادثات
        const chats = await client.getChats();

        // تصفية المحادثات للحصول على المجموعات فقط
        const groups = chats.filter(chat => chat.isGroup);

        console.log(`تم العثور على ${groups.length} مجموعة للمستخدم ${accountName}`);

        // معالجة كل مجموعة لاستخراج البيانات المطلوبة
        const groupsData = await Promise.all(groups.map(async (group) => {
            try {
                // الحصول على المشاركين في المجموعة مع معلومات إضافية
                const participants = group.participants.map(participant => {
                    return {
                        id: participant.id._serialized,
                        isAdmin: participant.isAdmin || false,
                        isSuperAdmin: participant.isSuperAdmin || false,
                        admin: participant.isAdmin ? 'admin' : (participant.isSuperAdmin ? 'superadmin' : false)
                    };
                });

                // تحديد إذا كان المستخدم الحالي مشرفًا
                const currentUserParticipant = participants.find(p => p.id === currentUserWid);
                const isCurrentUserAdmin = currentUserParticipant &&
                    (currentUserParticipant.isAdmin || currentUserParticipant.isSuperAdmin);

                // الحصول على رابط المجموعة إذا كان المستخدم مشرفًا
                let inviteCode = null;
                if (isCurrentUserAdmin) {
                    try {
                        inviteCode = await group.getInviteCode();
                    } catch (inviteErr) {
                        console.error(`خطأ في الحصول على رمز الدعوة للمجموعة ${group.name}:`, inviteErr.message);
                    }
                }

                // الحصول على معلومات العضوية المطلوبة إذا كان المستخدم مشرفًا
                let membershipRequests = [];
                if (isCurrentUserAdmin) {
                    try {
                        membershipRequests = await client.getGroupMembershipRequests(group.id._serialized);
                    } catch (membershipErr) {
                        console.error(`خطأ في الحصول على طلبات العضوية للمجموعة ${group.name}:`, membershipErr.message);
                    }
                }

                // إضافة معلومات إضافية للمشاركين
                const enhancedParticipants = participants.map(participant => {
                    let phoneNumber = null;
                    let realPhoneNumber = null;

                    // محاولة استخراج رقم الهاتف من المعرف
                    if (participant.id.endsWith('@c.us')) {
                        phoneNumber = participant.id.split('@')[0];
                        realPhoneNumber = convertCusToRealPhone(participant.id);
                    } else if (participant.id.endsWith('@lid')) {
                        phoneNumber = participant.id.split('@')[0];
                        realPhoneNumber = convertLidToRealPhone(participant.id);
                    }

                    return {
                        ...participant,
                        phoneNumber: phoneNumber,
                        realPhoneNumber: realPhoneNumber
                    };
                });

                return {
                    id: group.id._serialized,
                    name: group.name,
                    description: group.description || '',
                    owner: group.owner || '',
                    createdAt: group.createdAt ? new Date(group.createdAt * 1000).toISOString() : null,
                    participants: enhancedParticipants,
                    isCurrentUserAdmin: isCurrentUserAdmin,
                    inviteCode: inviteCode,
                    membershipRequests: membershipRequests.length,
                    membershipRequestsData: membershipRequests,
                    size: participants.length,
                };
            } catch (groupError) {
                console.error(`خطأ في معالجة المجموعة:`, groupError);
                return {
                    id: group.id._serialized,
                    name: group.name,
                    error: groupError.message
                };
            }
        }));

        // حفظ البيانات في ملف JSON
        const sessionDir = `./sessions/${accountName}`;
        if (!fs.existsSync(sessionDir)) {
            fs.mkdirSync(sessionDir, { recursive: true });
        }
        const groupsDataPath = path.join(sessionDir, 'groups_data.json');

        // تنظيم البيانات قبل الحفظ
        const groupsDataToSave = {
            accountName,
            extractedAt: new Date().toISOString(),
            count: groupsData.length,
            groups: groupsData
        };

        fs.writeFileSync(groupsDataPath, JSON.stringify(groupsDataToSave, null, 2), 'utf8');

        console.log(`تم استخراج وحفظ بيانات ${groupsData.length} مجموعة لـ ${accountName}`);

        return res.json({
            success: true,
            count: groupsData.length,
            groups: groupsData
        });
    } catch (error) {
        console.error(`خطأ في استخراج بيانات المجموعات للحساب ${accountName}:`, error);
        return res.status(500).json({
            error: 'حدث خطأ أثناء استخراج بيانات المجموعات',
            message: error.message
        });
    }
});

// API للحصول على جهات الاتصال
app.get('/api/contacts/:accountName', async (req, res) => {
    const { accountName } = req.params;
    const { refresh = 'false' } = req.query;

    try {
        // التحقق من وجود الحساب واتصاله
        if (!connections[accountName]) {
            return res.status(404).json({ error: 'الحساب غير موجود أو غير متصل' });
        }

        const client = connections[accountName].client;

        // التحقق من حالة الاتصال
        if (connections[accountName].status !== 'connected') {
            return res.status(400).json({ error: 'الحساب غير متصل حاليًا' });
        }

        // إذا لم يتم طلب التحديث، حاول استخدام البيانات المخزنة
        if (refresh === 'false') {
            const cachedContacts = loadContactsData(accountName);
            if (cachedContacts && cachedContacts.length > 0) {
                console.log(`Using cached contacts for ${accountName} (${cachedContacts.length} contacts)`);
                return res.json({
                    contacts: cachedContacts,
                    total: cachedContacts.length,
                    lastUpdated: getContactsLastUpdated(accountName)?.toISOString() || new Date().toISOString(),
                    fromCache: true
                });
            }
        }

        // إنشاء مصفوفة لتخزين جهات الاتصال
        const contacts = [];

        // إعداد الاستجابة لإرسال أحداث SSE
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');

        // إنشاء مصفوفة لتخزين معلومات التقدم
        const progressInfo = {
            total: 0,
            loaded: 0,
            step: 'initialization'
        };

        // وظيفة لإرسال تحديثات التقدم
        const sendProgressUpdate = () => {
            const data = JSON.stringify({
                progress: progressInfo.loaded / Math.max(progressInfo.total, 1) * 100,
                step: progressInfo.step,
                loaded: progressInfo.loaded,
                total: progressInfo.total
            });

            res.write(`data: ${data}\n\n`);
        };

        // إرسال تحديث أولي
        sendProgressUpdate();

        // إنشاء مؤقت لإرسال تحديثات التقدم كل ثانية
        const progressInterval = setInterval(sendProgressUpdate, 1000);

        // إضافة مستمع لإغلاق الاتصال
        req.on('close', () => {
            clearInterval(progressInterval);
        });

        try {
            // تحديث التقدم
            progressInfo.step = 'fetching_contacts';
            sendProgressUpdate();

            // الحصول على جهات الاتصال باستخدام مكتبة whatsapp-web.js
            console.log(`Fetching contacts for ${accountName} using whatsapp-web.js`);

            // الحصول على جهات الاتصال
            const whatsappContacts = await client.getContacts();

            // تصفية جهات الاتصال للأشخاص (وليس المجموعات)
            const filteredContacts = whatsappContacts.filter(contact => {
                return !contact.isGroup && !contact.isMe && contact.id &&
                       contact.id.user && contact.id._serialized &&
                       contact.id._serialized.endsWith('@c.us');
            });

            console.log(`Found ${filteredContacts.length} contacts for ${accountName} (with @c.us ID)`);

            // تحديث إجمالي عدد جهات الاتصال
            progressInfo.total = filteredContacts.length;
            sendProgressUpdate();

            // تحسين استرجاع صور الملفات الشخصية باستخدام دفعات
            const batchSize = 10; // عدد جهات الاتصال في كل دفعة للمعالجة المتوازية

            console.log(`بدء معالجة ${filteredContacts.length} جهة اتصال بحجم دفعة ${batchSize} لـ ${accountName}`);

            try {
                for (let i = 0; i < filteredContacts.length; i += batchSize) {
                    const currentBatchStart = i;
                    const currentBatchEnd = Math.min(i + batchSize, filteredContacts.length);
                    console.log(`معالجة الدفعة ${Math.floor(i/batchSize) + 1}/${Math.ceil(filteredContacts.length/batchSize)}: جهات الاتصال ${currentBatchStart+1}-${currentBatchEnd} من ${filteredContacts.length}`);

                    const batch = filteredContacts.slice(i, i + batchSize);
                    const batchPromises = batch.map(async (contact, batchIndex) => {
                        try {
                            // الرقم الحالي في الدفعة الكاملة
                            const currentIndex = i + batchIndex;

                            // الحصول على معرف جهة الاتصال
                            const contactId = contact.id._serialized;

                            // تنسيق رقم الهاتف بدون علامة +
                            const phoneNumber = contact.id.user;

                            // إضافة جهة الاتصال بمعلومات أساسية قبل استكمال الباقي
                            // هذا يضمن أن لدينا بيانات حتى لو فشلت المعالجة الإضافية
                            const contactData = {
                                id: contactId,
                                pushName: contact.pushname || '', // الاسم المسجل في واتساب
                                savedName: contact.name || '', // الاسم المحفوظ في جهات الاتصال
                                name: contact.pushname || contact.name || phoneNumber, // الاسم الذي سيتم عرضه
                                verifiedName: contact.verifiedName || '', // الاسم الموثق (للحسابات التجارية)
                                number: phoneNumber,
                                profilePictureUrl: null,
                                isBusiness: contact.isBusiness || false
                            };

                            // إضافة جهة الاتصال بشكل مبكر - سنحدث الصورة لاحقًا
                            contacts.push(contactData);

                            // تحاول الحصول على صورة الملف الشخصي بحد أقصى من الوقت
                            try {
                                const profilePicturePromise = client.getProfilePicUrl(contactId);
                                // إضافة مهلة زمنية لتجنب التجميد
                                const timeoutPromise = new Promise((_, reject) =>
                                    setTimeout(() => reject(new Error('استغرق الحصول على الصورة وقتًا طويلاً')), 5000)
                                );

                                // استخدام Promise.race للحصول على نتيجة أو فشل بعد المهلة
                                const profilePictureUrl = await Promise.race([profilePicturePromise, timeoutPromise])
                                    .catch(() => null);

                                // تحديث الصورة في البيانات المحفوظة مسبقًا
                                if (profilePictureUrl) {
                                    contactData.profilePictureUrl = profilePictureUrl;
                                }
                            } catch (imageError) {
                                console.warn(`فشل في الحصول على صورة جهة الاتصال ${currentIndex+1}/${filteredContacts.length} (${phoneNumber}): ${imageError.message}`);
                            }

                            // تحديث عدد جهات الاتصال التي تم تحميلها
                            progressInfo.loaded++;

                            // إرسال تحديثات للتقدم بشكل أكثر تواترًا
                            if (currentIndex % 5 === 0 || currentIndex === filteredContacts.length - 1) {
                                sendProgressUpdate();
                            }
                        } catch (contactError) {
                            console.error(`خطأ في معالجة جهة الاتصال ${i + batchIndex + 1}/${filteredContacts.length}: ${contactError.message}`);

                            // زيادة عداد التحميل حتى لو حدث خطأ
                            progressInfo.loaded++;

                            // إضافة جهة اتصال بسيطة حتى لا نفقد معلومات
                            try {
                                if (contact && contact.id) {
                                    const phoneNumber = contact.id.user;
                                    contacts.push({
                                        id: contact.id._serialized || 'unknown-' + phoneNumber,
                                        name: phoneNumber,
                                        number: phoneNumber,
                                        pushName: '',
                                        savedName: '',
                                        verifiedName: '',
                                        profilePictureUrl: null,
                                        isBusiness: false
                                    });
                                }
                            } catch (recoveryError) {
                                console.error(`فشل في إضافة جهة اتصال بسيطة: ${recoveryError.message}`);
                            }
                        }
                    });

                    // تنفيذ مع معالجة أخطاء على مستوى الدفعة
                    try {
                        // انتظار اكتمال معالجة الدفعة الحالية
                        await Promise.all(batchPromises);
                        console.log(`تم استكمال الدفعة ${Math.floor(i/batchSize) + 1} بنجاح`);
                    } catch (batchError) {
                        console.error(`خطأ أثناء معالجة دفعة جهات الاتصال: ${batchError.message}`);
                        // نستمر في المعالجة رغم الخطأ لضمان الحصول على أكبر قدر ممكن من البيانات
                    }

                    // إرسال تحديث التقدم بعد كل دفعة
                    sendProgressUpdate();

                    // تعزيز استقرار البرنامج بإتاحة وقت للتنفيذ الآخر
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                console.log(`تم استكمال معالجة جميع جهات الاتصال (${contacts.length} جهات اتصال)`);
            } catch (processingError) {
                console.error(`خطأ غير متوقع أثناء معالجة جهات الاتصال: ${processingError.message}`);
                console.error(processingError.stack);

                // إذا حدث خطأ، لكن قمنا بالفعل بمعالجة بعض جهات الاتصال، نستمر
                if (contacts.length === 0) {
                    // فشلنا في الحصول على أي جهات اتصال
                    clearInterval(progressInterval);
                    res.write(`event: error\ndata: ${JSON.stringify({ message: 'فشل استخراج جهات الاتصال: ' + processingError.message })}\n\n`);
                    res.end();
                    return;
                }

                console.log(`سنستمر بـ ${contacts.length} جهة اتصال تم استخراجها قبل الخطأ`);
            }

            // إذا لم يتم العثور على جهات اتصال، أضف الحساب الحالي كجهة اتصال افتراضية
            if (contacts.length === 0) {
                console.log(`No contacts found for ${accountName}, adding self as default contact`);

                // تحديث التقدم
                progressInfo.step = 'adding_self';
                progressInfo.total = 1;
                progressInfo.loaded = 0;
                sendProgressUpdate();

                // الحصول على معلومات الحساب الحالي
                const clientInfo = client.info;

                try {
                    // الحصول على صورة الملف الشخصي
                    const profilePictureUrl = await client.getProfilePicUrl(clientInfo.wid._serialized).catch(() => null);

                    // إضافة الحساب الحالي كجهة اتصال افتراضية
                    contacts.push({
                        id: clientInfo.wid._serialized,
                        pushName: clientInfo.pushname || 'أنا', // الاسم المسجل في واتساب
                        savedName: 'أنا', // الاسم المحفوظ في جهات الاتصال
                        name: clientInfo.pushname || 'أنا', // الاسم الذي سيتم عرضه
                        verifiedName: '',
                        number: clientInfo.wid.user,
                        profilePictureUrl,
                        isBusiness: false
                    });

                    // تحديث عدد جهات الاتصال التي تم تحميلها
                    progressInfo.loaded = 1;
                    sendProgressUpdate();
                } catch (selfContactError) {
                    console.error(`Error adding self as contact: ${selfContactError.message}`);
                }
            }

            // تحديث التقدم
            progressInfo.step = 'sorting_contacts';
            sendProgressUpdate();

            // إزالة التكرار في جهات الاتصال بناءً على رقم الهاتف
            const uniqueContacts = [];
            const phoneMap = new Map();

            // تجميع جهات الاتصال المكررة
            for (const contact of contacts) {
                const phoneNumber = contact.number;

                if (phoneMap.has(phoneNumber)) {
                    // دمج معلومات جهة الاتصال الحالية مع جهة الاتصال الموجودة
                    const existingContact = phoneMap.get(phoneNumber);

                    // دمج الأسماء (الأولوية للقيم غير الفارغة)
                    existingContact.pushName = existingContact.pushName || contact.pushName;
                    existingContact.savedName = existingContact.savedName || contact.savedName;
                    existingContact.name = existingContact.name || contact.name;
                    existingContact.verifiedName = existingContact.verifiedName || contact.verifiedName;

                    // احتفظ بصورة الملف الشخصي إذا كانت متوفرة
                    existingContact.profilePictureUrl = existingContact.profilePictureUrl || contact.profilePictureUrl;

                    // احتفظ بمعلومات الأعمال
                    existingContact.isBusiness = existingContact.isBusiness || contact.isBusiness;
                } else {
                    // إضافة جهة الاتصال إلى الخريطة
                    phoneMap.set(phoneNumber, contact);
                    uniqueContacts.push(contact);
                }
            }

            // فرز جهات الاتصال أبجديًا
            const sortedContacts = uniqueContacts.sort((a, b) => {
                const nameA = a.pushName || a.savedName || a.name || a.number;
                const nameB = b.pushName || b.savedName || b.name || b.number;
                return nameA.localeCompare(nameB);
            });

            // تحديث التقدم
            progressInfo.step = 'saving_contacts';
            sendProgressUpdate();



            // حفظ جهات الاتصال في ملف JSON
            try {
                // التأكد من صحة المتغير قبل حفظه
                if (!Array.isArray(sortedContacts)) {
                    console.error(`خطأ: المتغير sortedContacts ليس مصفوفة صالحة لـ ${accountName}، النوع: ${typeof sortedContacts}`);
                    // إرسال خطأ إلى العميل وإنهاء الاستجابة
                    clearInterval(progressInterval);
                    res.write(`event: error\ndata: ${JSON.stringify({ message: 'خطأ داخلي: بيانات جهات الاتصال غير صالحة.' })}\n\n`);
                    res.end();
                    return;
                } else if (sortedContacts.length === 0) {
                    console.warn(`تحذير: لا توجد جهات اتصال للحفظ لـ ${accountName}`);
                    // مع ذلك، سنحاول حفظ مصفوفة فارغة إذا كان هذا هو المقصود
                } else {
                    console.log(`جاري حفظ ${sortedContacts.length} جهة اتصال لـ ${accountName}...`);
                    if (sortedContacts.length > 0) {
                        console.log(`نموذج من البيانات (أول جهة اتصال): ${JSON.stringify(sortedContacts[0], null, 2)}`);
                    }
                }

                const sessionDir = path.join(__dirname, 'sessions', accountName);
                if (!fs.existsSync(sessionDir)) {
                    fs.mkdirSync(sessionDir, { recursive: true });
                    console.log(`تم إنشاء مجلد: ${sessionDir}`);
                }

                console.log(`محاولة حفظ جهات الاتصال لـ ${accountName} باستخدام دالة saveContactsData...`);
                const success = saveContactsData(accountName, sortedContacts);

                if (success) {
                    console.log(`تم حفظ بيانات جهات الاتصال بنجاح لـ ${accountName} (${sortedContacts.length} جهات اتصال)`);
                    const verifyContacts = loadContactsData(accountName);
                    if (Array.isArray(verifyContacts)) {
                        console.log(`تأكيد: تم التحقق من وجود ${verifyContacts.length} جهة اتصال في الملف المحفوظ`);
                        progressInfo.step = 'completed';
                        progressInfo.loaded = progressInfo.total;
                        sendProgressUpdate();
                        clearInterval(progressInterval);

                        // التحقق من حفظ البيانات بنجاح
                        const contactsPath = path.join(__dirname, 'sessions', accountName, 'contacts.json');
                        if (fs.existsSync(contactsPath)) {
                            console.log(`تم التأكد من وجود ملف جهات الاتصال: ${contactsPath}`);

                            // التأكد من حجم الملف ومحتواه
                            const stats = fs.statSync(contactsPath);
                            if (stats.size > 0) {
                                console.log(`حجم ملف جهات الاتصال: ${stats.size} بايت`);

                                // إرسال الاستجابة الناجحة
                                const jsonResponse = {
                                    contacts: sortedContacts,
                                    total: sortedContacts.length,
                                    lastUpdated: new Date().toISOString(),
                                    success: true
                                };

                                res.write(`event: contactsData\ndata: ${JSON.stringify(jsonResponse)}\n\n`);
                                res.end();
                                return;
                            } else {
                                console.error(`خطأ: ملف جهات الاتصال فارغ بعد الحفظ (0 بايت)`);
                            }
                        } else {
                            console.error(`خطأ: ملف جهات الاتصال غير موجود بعد محاولة الحفظ`);
                        }
                    } else {
                        console.error(`خطأ في التحقق: نتيجة loadContactsData ليست مصفوفة، النوع: ${typeof verifyContacts}`);
                        // محاولة إصلاح المشكلة بإعادة حفظ البيانات مباشرة
                        const contactsPath = path.join(__dirname, 'sessions', accountName, 'contacts.json');
                        fs.writeFileSync(contactsPath, JSON.stringify(sortedContacts, null, 2), 'utf8');
                        console.log(`تمت محاولة إصلاح ملف جهات الاتصال بالكتابة المباشرة`);

                        // إرسال الاستجابة الناجحة
                        const jsonResponse = {
                            contacts: sortedContacts,
                            total: sortedContacts.length,
                            lastUpdated: new Date().toISOString(),
                            recovered: true
                        };

                        res.write(`event: contactsData\ndata: ${JSON.stringify(jsonResponse)}\n\n`);
                        res.end();
                        return;
                    }
                } else {
                    console.error(`فشل في حفظ بيانات جهات الاتصال لـ ${accountName} (saveContactsData ردت بـ false)`);

                    // محاولة الكتابة المباشرة في الملف
                    try {
                        const contactsPath = path.join(__dirname, 'sessions', accountName, 'contacts.json');
                        fs.writeFileSync(contactsPath, JSON.stringify(sortedContacts, null, 2), 'utf8');
                        console.log(`تمت محاولة إصلاح المشكلة بالكتابة المباشرة في الملف: ${contactsPath}`);

                        // التحقق من نجاح الكتابة المباشرة
                        if (fs.existsSync(contactsPath) && fs.statSync(contactsPath).size > 0) {
                            console.log(`نجحت الكتابة المباشرة، حجم الملف: ${fs.statSync(contactsPath).size} بايت`);

                            // إرسال الاستجابة الناجحة
                            const jsonResponse = {
                                contacts: sortedContacts,
                                total: sortedContacts.length,
                                lastUpdated: new Date().toISOString(),
                                directSave: true
                            };

                            res.write(`event: contactsData\ndata: ${JSON.stringify(jsonResponse)}\n\n`);
                            res.end();
                            return;
                        }
                    } catch (directSaveError) {
                        console.error(`فشل في الكتابة المباشرة في ملف جهات الاتصال: ${directSaveError.message}`);
                    }

                    // إرسال خطأ إلى العميل وإنهاء الاستجابة
                    clearInterval(progressInterval);
                    res.write(`event: error\ndata: ${JSON.stringify({ message: 'فشل حفظ جهات الاتصال على الخادم.' })}\n\n`);
                    res.end();
                    return;
                }
            } catch (saveError) {
                console.error(`خطأ في حفظ بيانات جهات الاتصال: ${saveError.message}`);
                console.error(saveError.stack); // طباعة تتبع الخطأ كاملاً للتشخيص

                // محاولة الكتابة المباشرة في الملف كمحاولة أخيرة للإصلاح
                try {
                    const contactsPath = path.join(__dirname, 'sessions', accountName, 'contacts.json');
                    fs.writeFileSync(contactsPath, JSON.stringify(sortedContacts, null, 2), 'utf8');
                    console.log(`تمت محاولة الإنقاذ بعد الخطأ بالكتابة المباشرة في الملف: ${contactsPath}`);

                    if (fs.existsSync(contactsPath) && fs.statSync(contactsPath).size > 0) {
                        const jsonResponse = {
                            contacts: sortedContacts,
                            total: sortedContacts.length,
                            lastUpdated: new Date().toISOString(),
                            emergency: true
                        };

                        res.write(`event: contactsData\ndata: ${JSON.stringify(jsonResponse)}\n\n`);
                        res.end();
                        return;
                    }
                } catch (finalError) {
                    console.error(`فشل في المحاولة النهائية لحفظ جهات الاتصال: ${finalError.message}`);
                }
            }

            // تحديث التقدم
            progressInfo.step = 'completed';
            progressInfo.loaded = progressInfo.total;
            sendProgressUpdate();

            // إيقاف مؤقت التقدم
            clearInterval(progressInterval);

            // إرسال البيانات النهائية
            const jsonResponse = {
                contacts: sortedContacts,
                total: sortedContacts.length,
                lastUpdated: new Date().toISOString()
            };

            // إرسال البيانات كحدث خاص
            res.write(`event: contactsData\ndata: ${JSON.stringify(jsonResponse)}\n\n`);

            // إنهاء الاستجابة
            res.end();
            return;
        } catch (error) {
            // إيقاف إرسال تحديثات التقدم
            clearInterval(progressInterval);

            console.error(`Error fetching contacts for ${accountName}:`, error);
            throw error;
        }
    } catch (error) {
        console.error(`Error fetching contacts for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء جلب جهات الاتصال' });
    }
});

// API للحصول على جهات الاتصال من ملف JSON المخزن (دون تحديث)
app.get('/api/cached-contacts/:accountName', (req, res) => {
    const { accountName } = req.params;
    const { page = '1', pageSize = '20' } = req.query;

    try {
        console.log(`طلب استدعاء جهات الاتصال المخزنة لـ ${accountName}`);

        // التحقق من وجود الحساب
        const sessionDir = path.join(__dirname, 'sessions', accountName);
        if (!fs.existsSync(sessionDir)) {
            fs.mkdirSync(sessionDir, { recursive: true });
            console.log(`تم إنشاء مجلد جلسة جديد لـ ${accountName}: ${sessionDir}`);
        }

        // تحميل جهات الاتصال من الملف المخزن
        console.log(`محاولة تحميل جهات الاتصال لـ ${accountName} باستخدام دالة loadContactsData...`);
        const contacts = loadContactsData(accountName);

        // إذا لم يكن هناك جهات اتصال (الدالة المعدلة ستنشئ ملف فارغ وتعيد مصفوفة فارغة)
        if (!contacts || contacts.length === 0) {
            console.log(`لم يتم العثور على جهات اتصال مخزنة لـ ${accountName}`);
            return res.json({
                contacts: [],
                lastUpdated: new Date().toISOString(),
                pagination: {
                    page: 1,
                    pageSize: parseInt(pageSize),
                    totalPages: 0,
                    totalItems: 0
                },
                message: 'تم إنشاء ملف جهات اتصال فارغ'
            });
        }

        // التحقق من صحة البيانات والتأكد من أنها تحتوي على الحقول المطلوبة
        const validContacts = contacts.filter(contact => {
            return contact && typeof contact === 'object' && (contact.id || contact.number);
        });

        if (validContacts.length < contacts.length) {
            console.warn(`تحذير: تم استبعاد ${contacts.length - validContacts.length} جهة اتصال غير صالحة`);
            // حفظ البيانات المنقحة لاستخدامها لاحقًا
            saveContactsData(accountName, validContacts);
        }

        console.log(`تم العثور على ${validContacts.length} جهة اتصال صالحة مخزنة لـ ${accountName}`);

        // الحصول على تاريخ آخر تحديث
        const lastUpdated = getContactsLastUpdated(accountName);

        // تفعيل التصفح بالصفحات إذا تم طلبه
        const pageNum = parseInt(page);
        const pageSizeNum = parseInt(pageSize);

        // إذا كان pageSize كبير (مثل >=1000)، نفترض أنه يطلب كل البيانات
        if (pageSizeNum >= 1000) {
            console.log(`إرسال كل جهات الاتصال المخزنة (${validContacts.length}) لـ ${accountName}`);
            return res.json({
                contacts: validContacts,
                lastUpdated: lastUpdated || new Date().toISOString(),
                pagination: {
                    page: 1,
                    pageSize: validContacts.length,
                    totalPages: 1,
                    totalItems: validContacts.length
                }
            });
        }

        // حساب الفهارس للصفحة المطلوبة
        const startIndex = (pageNum - 1) * pageSizeNum;
        const endIndex = Math.min(startIndex + pageSizeNum, validContacts.length);

        // استخراج جهات الاتصال للصفحة الحالية
        const paginatedContacts = validContacts.slice(startIndex, endIndex);

        // إنشاء معلومات الصفحات
        const pagination = {
            page: pageNum,
            pageSize: pageSizeNum,
            totalPages: Math.ceil(validContacts.length / pageSizeNum),
            totalItems: validContacts.length
        };

        console.log(`إرسال ${paginatedContacts.length} جهة اتصال (الصفحة ${pageNum}/${pagination.totalPages}) لـ ${accountName}`);

        // إرسال البيانات
        res.json({
            contacts: paginatedContacts,
            pagination,
            lastUpdated: lastUpdated || new Date().toISOString()
        });
    } catch (error) {
        console.error(`خطأ في تحميل جهات الاتصال المخزنة لـ ${accountName}:`, error);
        console.error(error.stack); // طباعة تتبع الخطأ كاملاً للتشخيص

        // محاولة التعافي والإرجاع
        try {
            // إنشاء ملف فارغ في حالة الخطأ
            const contactsPath = path.join(__dirname, 'sessions', accountName, 'contacts.json');
            if (!fs.existsSync(path.dirname(contactsPath))) {
                fs.mkdirSync(path.dirname(contactsPath), { recursive: true });
            }
            fs.writeFileSync(contactsPath, JSON.stringify([], null, 2), 'utf8');

            res.status(500).json({
                error: 'حدث خطأ أثناء استرجاع جهات الاتصال المخزنة',
                message: 'تم إنشاء ملف جهات اتصال فارغ كإجراء للتعافي',
                contacts: [],
                pagination: {
                    page: 1,
                    pageSize: parseInt(pageSize),
                    totalPages: 0,
                    totalItems: 0
                },
                lastUpdated: new Date().toISOString()
            });
        } catch (recoveryError) {
            console.error(`فشل في التعافي من خطأ تحميل جهات الاتصال لـ ${accountName}:`, recoveryError);
            res.status(500).json({ error: 'حدث خطأ أثناء استرجاع جهات الاتصال المخزنة' });
        }
    }
});

// دالة مساعدة للكشف عن صحة ملفات الجلسة
function checkSessionHealth(accountName) {
    try {
        // 1. التحقق من مجلد الجلسة وملفاته الأساسية
        const sessionDir = path.join(__dirname, '.wwebjs_auth', `session-${accountName}`);
        if (!fs.existsSync(sessionDir)) {
            return { valid: false, reason: 'session_dir_not_found' };
        }

        // 2. التحقق من وجود ملف الجلسة الأساسي
        // في whatsapp-web.js، عادة ما تكون هناك ملفات متعددة، لكن سنتحقق من وجود مجلد الاعتماد
        const mainSessionFileName = path.join(sessionDir, 'Default', 'Local Storage', 'leveldb');
        if (!fs.existsSync(mainSessionFileName)) {
            return { valid: false, reason: 'session_files_missing' };
        }

        // 3. إذا كان الاتصال موجودًا، تحقق من حالته
        if (connections[accountName]) {
            if (connections[accountName].status === 'error' ||
                connections[accountName].status === 'auth_failure' ||
                connections[accountName].status === 'disconnected') {
                return { valid: false, reason: connections[accountName].status };
            }

            return { valid: true, status: connections[accountName].status };
        }

        // 4. التحقق إذا كانت هناك معلومات حساب محفوظة للجلسة غير المتصلة
        const savedAccountInfo = loadAccountData(accountName);
        if (!savedAccountInfo) {
            // لم نجد معلومات الحساب، لكن المجلد موجود، لذا نعتبره محتملاً
            return { valid: true, status: 'disconnected', note: 'no_account_info' };
        }

        return { valid: true, status: 'disconnected' };
    } catch (error) {
        console.error(`Error checking session health for ${accountName}:`, error);
        return { valid: false, reason: 'error_checking', error: error.message };
    }
}

// API للحصول على جميع الحسابات
app.get('/api/accounts', (req, res) => {
    try {
        const accountsList = [];

        // 1. جمع الحسابات النشطة من الاتصالات
        for (const accountName in connections) {
            // محاولة قراءة معلومات الحساب من ملف JSON
            const savedAccountInfo = loadAccountData(accountName);

            // استخدام المعلومات المحفوظة في ملف JSON إذا كانت متوفرة، أو من الاتصال النشط
            const info = connections[accountName]?.info || savedAccountInfo;
            const currentPhoneNumber = connections[accountName]?.phoneNumber || savedAccountInfo?.number;

            // فحص صحة الجلسة
            const sessionHealth = checkSessionHealth(accountName);

            accountsList.push({
                accountName,
                info: info,
                phoneNumber: currentPhoneNumber,
                status: connections[accountName]?.status || (savedAccountInfo ? 'disconnected' : 'unknown'),
                sessionHealth,
                needsRepair: sessionHealth.valid === false // إضافة علامة للحسابات التي تحتاج إلى إصلاح
            });
        }

        // 2. إضافة الحسابات المحفوظة التي ليست بالضرورة متصلة حاليًا
        const sessionAuthDir = path.join(__dirname, '.wwebjs_auth');
        if (fs.existsSync(sessionAuthDir)) {
            const sessionFolders = fs.readdirSync(sessionAuthDir).filter(folder => folder.startsWith('session-'));
            sessionFolders.forEach(folderName => {
                const accountNameFromFolder = folderName.replace('session-', '');
                if (!accountsList.find(acc => acc.accountName === accountNameFromFolder)) {
                    const savedAccountInfo = loadAccountData(accountNameFromFolder);

                    // فحص صحة الجلسة
                    const sessionHealth = checkSessionHealth(accountNameFromFolder);

                    accountsList.push({
                        accountName: accountNameFromFolder,
                        info: savedAccountInfo,
                        phoneNumber: savedAccountInfo?.number,
                        status: 'disconnected',
                        sessionHealth,
                        needsRepair: sessionHealth.valid === false // إضافة علامة للحسابات التي تحتاج إلى إصلاح
                    });
                }
            });
        }

        return res.json({ accounts: accountsList });
    } catch (error) {
        console.error('Error loading accounts:', error);
        return res.status(500).json({ error: 'حدث خطأ أثناء تحميل الحسابات' });
    }
});

// API لحذف الحساب وإزالة الجلسة المرتبطة به
app.delete('/api/delete-account/:accountName', async (req, res) => {
    try {
        const { accountName } = req.params;
        const sessionDir = path.join(__dirname, '.wwebjs_auth', `session-${accountName}`);
        const legacySessionDir = path.join(__dirname, 'sessions', accountName); // مجلد الجلسة القديم لـ Baileys

        let accountExists = false;
        if (connections[accountName] || fs.existsSync(sessionDir) || fs.existsSync(legacySessionDir)) {
            accountExists = true;
        }

        if (!accountExists) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // إذا كان الاتصال موجودًا، قم بتسجيل الخروج وتدميره
        if (connections[accountName] && connections[accountName].client) {
            try {
                console.log(`Logging out and destroying client for ${accountName}...`);
                await connections[accountName].client.logout(); // يسجل الخروج من WhatsApp Web
                await connections[accountName].client.destroy(); // يغلق المتصفح ويحرر الموارد
                console.log(`Client logged out and destroyed for ${accountName}`);
            } catch (e) {
                console.error(`Error logging out or destroying client for ${accountName}:`, e);
            }
            delete connections[accountName];
        }

        // حذف مجلد الجلسة الخاص بـ whatsapp-web.js
        if (fs.existsSync(sessionDir)) {
            try {
                // استخدام fs.rmSync لحذف المجلد ومحتوياته بشكل متكرر
                fs.rmSync(sessionDir, { recursive: true, force: true });
                console.log(`Session directory ${sessionDir} deleted for ${accountName}`);
            } catch (err) {
                console.error(`Error deleting session directory ${sessionDir} for ${accountName}:`, err);
            }
        }

        // حذف مجلد الجلسة القديم الخاص بـ Baileys إذا كان موجودًا
        if (fs.existsSync(legacySessionDir)) {
            try {
                fs.rmSync(legacySessionDir, { recursive: true, force: true });
                console.log(`Legacy session directory ${legacySessionDir} deleted for ${accountName}`);
            } catch (err) {
                console.error(`Error deleting legacy session directory ${legacySessionDir} for ${accountName}:`, err);
            }
        }


        // حذف ملف بيانات الحساب JSON
        const accountDataPath = path.join(__dirname, 'accounts_data', `${accountName}.json`);
        if (fs.existsSync(accountDataPath)) {
            try {
                fs.unlinkSync(accountDataPath);
                console.log(`Account data file ${accountDataPath} deleted for ${accountName}`);
            } catch (err) {
                console.error(`Error deleting account data file ${accountDataPath} for ${accountName}:`, err);
            }
        }

        // حذف ملف بيانات جهات الاتصال JSON
        const contactsDataPath = path.join(__dirname, 'contacts_data', `${accountName}.json`);
        if (fs.existsSync(contactsDataPath)) {
            try {
                fs.unlinkSync(contactsDataPath);
                console.log(`Contacts data file ${contactsDataPath} deleted for ${accountName}`);
            } catch (err) {
                console.error(`Error deleting contacts data file ${contactsDataPath} for ${accountName}:`, err);
            }
        }


        return res.status(200).json({
            success: true,
            message: `تم حذف الحساب ${accountName} وجميع بيانات الجلسة المرتبطة به بنجاح`
        });
    } catch (error) {
        console.error('Error deleting account:', error);
        return res.status(500).json({ error: 'حدث خطأ أثناء حذف الحساب' });
    }
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// وظيفة لاستعادة الاتصالات المحفوظة عند بدء التشغيل
async function restoreConnections() {
    try {
        const authDir = path.join(__dirname, '.wwebjs_auth');
        if (!fs.existsSync(authDir)) {
            console.log('No .wwebjs_auth directory found. Skipping connection restoration.');
            return;
        }

        const sessionFolders = fs.readdirSync(authDir).filter(folder => folder.startsWith('session-'));
        console.log(`Found ${sessionFolders.length} saved sessions. Attempting to restore connections...`);

        for (const folderName of sessionFolders) {
            const accountName = folderName.replace('session-', '');
            try {
                // التحقق مما إذا كان الاتصال قيد التهيئة بالفعل لتجنب التكرار
                if (connections[accountName] && (connections[accountName].status === 'initializing' || connections[accountName].status === 'qr_ready')) {
                    console.log(`Connection for ${accountName} is already being initialized. Skipping duplicate restoration.`);
                    continue;
                }

                console.log(`Restoring connection for ${accountName}...`);
                // لا نحتاج إلى رقم الهاتف هنا لأن LocalAuth تتعامل معه
                // createWhatsAppConnection ستستخدم LocalAuth التي ستحمل الجلسة المحفوظة
                await createWhatsAppConnection(accountName, null);
            } catch (error) {
                console.error(`Error restoring connection for ${accountName}:`, error);
            }
        }
        console.log('Connection restoration process completed.');
    } catch (error) {
        console.error('Error in restoreConnections:', error);
    }
}

// إضافة مسار API جديد لإصلاح الحسابات المعطوبة
app.post('/api/repair-account/:accountName', async (req, res) => {
    try {
        const { accountName } = req.params;

        // التحقق من وجود الحساب
        const sessionDir = path.join(__dirname, '.wwebjs_auth', `session-${accountName}`);
        if (!fs.existsSync(sessionDir)) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // إذا كان الاتصال موجوداً، قم بتدميره أولاً
        if (connections[accountName]) {
            try {
                if (connections[accountName].client) {
                    await connections[accountName].client.destroy().catch(e => console.warn(`Destroy error: ${e.message}`));
                }
                delete connections[accountName];
                console.log(`تم تدمير الاتصال الحالي للحساب ${accountName}`);
            } catch (e) {
                console.warn(`فشل في تنظيف الاتصال السابق: ${e.message}`);
            }
        }

        // حذف مجلد الجلسة المعطوب
        try {
            fs.rmSync(sessionDir, { recursive: true, force: true });
            console.log(`تم حذف مجلد الجلسة المعطوب للحساب ${accountName}`);
        } catch (err) {
            console.error(`خطأ في حذف مجلد الجلسة المعطوب للحساب ${accountName}:`, err);
        }

        // إنشاء اتصال جديد
        const savedAccountInfo = loadAccountData(accountName);
        const phoneNumber = savedAccountInfo?.number || req.body.phoneNumber;

        const success = await createWhatsAppConnection(accountName, phoneNumber);

        if (success) {
            return res.status(200).json({
                success: true,
                message: `تم إصلاح الحساب ${accountName} بنجاح، يرجى متابعة حالة الاتصال`
            });
        } else {
            return res.status(500).json({ error: 'فشل في إصلاح الحساب، يرجى المحاولة مرة أخرى' });
        }
    } catch (error) {
        console.error('Error repairing account:', error);
        return res.status(500).json({ error: 'حدث خطأ أثناء إصلاح الحساب' });
    }
});

// بدء الخادم واستعادة الاتصالات
app.listen(PORT, async () => {
    console.log(`Server running on port ${PORT}`);
    await restoreConnections();
});

// API لاستخراج بيانات المجموعات وطباعتها في سجلات الخادم
app.get('/api/debug-groups/:accountName', async (req, res) => {
    const { accountName } = req.params;

    try {
        // التحقق من وجود الحساب واتصاله
        if (!connections[accountName]) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // التحقق من اتصال الحساب
        if (connections[accountName].status !== 'connected') {
            return res.status(400).json({ error: 'الحساب غير متصل' });
        }

        const client = connections[accountName].client;

        // الحصول على معرف المستخدم الحالي
        const currentUserWid = client.info.wid._serialized;

        // الحصول على جميع المحادثات
        const chats = await client.getChats();

        // تصفية المجموعات فقط
        const groups = chats.filter(chat => chat.isGroup);

        console.log(`Found ${groups.length} groups for ${accountName}`);

        // إنشاء كائن لتخزين بيانات المجموعات
        const groupsData = {};

        // معالجة كل مجموعة
        for (const group of groups) {
            try {
                // استخراج بيانات المشاركين في المجموعة
                const participants = group.participants.map(participant => {
                    return {
                        id: participant.id._serialized,
                        isAdmin: participant.isAdmin || false,
                        isSuperAdmin: participant.isSuperAdmin || false,
                        admin: participant.isAdmin ? 'admin' : (participant.isSuperAdmin ? 'superadmin' : false)
                    };
                });

                // التحقق مما إذا كان المستخدم الحالي مديرًا في المجموعة
                const currentUserParticipant = participants.find(p => p.id === currentUserWid);
                const isCurrentUserAdmin = currentUserParticipant &&
                    (currentUserParticipant.isAdmin || currentUserParticipant.isSuperAdmin);

                // الحصول على رابط دعوة المجموعة (إذا كان المستخدم مديرًا)
                let inviteCode = null;
                if (isCurrentUserAdmin) {
                    try {
                        inviteCode = await group.getInviteCode();
                    } catch (inviteError) {
                        console.error(`Error getting invite code for group ${group.id._serialized}:`, inviteError);
                    }
                }

                // الحصول على طلبات العضوية للمجموعة (إذا كان المستخدم مديرًا)
                let membershipRequests = [];
                if (isCurrentUserAdmin) {
                    try {
                        membershipRequests = await group.getGroupMembershipRequests();
                        // تحويل بيانات طلبات العضوية إلى صيغة مبسطة
                        membershipRequests = membershipRequests.map(request => ({
                            id: request.id?._serialized || '',
                            addedBy: request.addedBy?._serialized || '',
                            requestMethod: request.requestMethod || '',
                            timestamp: request.t || 0
                        }));
                    } catch (requestsError) {
                        console.error(`Error getting membership requests for group ${group.id._serialized}:`, requestsError);
                    }
                }

                // إضافة بيانات المجموعة إلى الكائن
                groupsData[group.id._serialized] = {
                    id: group.id._serialized,
                    subject: group.name,
                    name: group.name, // إضافة الاسم في حقل name للتوافق مع باقي المسارات
                    participants: participants,
                    isCurrentUserAdmin: isCurrentUserAdmin,
                    creation: group.timestamp || Date.now(),
                    createdAt: group.createdAt || null,
                    owner: group.owner?._serialized || '',
                    desc: group.description || '',
                    size: participants.length,
                    // إضافة المزيد من بيانات المجموعة
                    inviteCode: inviteCode,
                    membershipRequests: membershipRequests,
                    isArchived: group.archived || false,
                    isMuted: group.isMuted || false,
                    unreadCount: group.unreadCount || 0,
                    isReadOnly: group.isReadOnly || false,
                    isPinned: group.pinned || false,
                    muteExpiration: group.muteExpiration || 0,
                    lastMessage: group.lastMessage ? {
                        body: group.lastMessage.body || '',
                        timestamp: group.lastMessage.timestamp || 0
                    } : null
                };
            } catch (error) {
                console.error(`Error processing group ${group.id._serialized}:`, error);
            }
        }

        // حفظ بيانات المجموعات في ملف JSON
        try {
            // التأكد من وجود المجلد
            const sessionDir = path.join(__dirname, 'sessions', accountName);
            if (!fs.existsSync(sessionDir)) {
                fs.mkdirSync(sessionDir, { recursive: true });
                console.log(`Created directory: ${sessionDir}`);
            }

            // حفظ الملف
            const groupsDataPath = path.join(sessionDir, 'groups_data.json');
            fs.writeFileSync(groupsDataPath, JSON.stringify(groupsData, null, 2), 'utf8');
            console.log(`تم حفظ بيانات المجموعات في: ${groupsDataPath}`);
        } catch (saveError) {
            console.error(`Error saving groups data: ${saveError.message}`);
        }

        // تحليل بيانات المجموعات للإرجاع
        const groupsAnalysis = Object.values(groupsData).map(group => ({
            id: group.id,
            name: group.name || group.subject, // استخدام name إذا كان موجودًا أو subject كبديل
            participants: group.participants.length,
            isAdmin: group.isCurrentUserAdmin,
            adminType: group.isCurrentUserAdmin ? 'admin' : 'member',
            hasInviteCode: !!group.inviteCode,
            membershipRequests: group.membershipRequests.length
        }));

        // إرجاع نتائج التحليل
        return res.json({
            success: true,
            message: `تم تحليل ${groupsAnalysis.length} مجموعة`,
            filePath: path.join('sessions', accountName, 'groups_data.json'),
            groups: groupsAnalysis
        });
    } catch (error) {
        console.error(`Error analyzing groups for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء تحليل المجموعات' });
    }
});

// API للحصول على ملف groups_data.json مباشرة
app.get('/api/get-groups-json/:accountName', (req, res) => {
    const { accountName } = req.params;

    try {
        // مسار ملف بيانات المجموعات
        const groupsDataPath = path.join(__dirname, 'sessions', accountName, 'groups_data.json');

        // التحقق من وجود الملف
        if (!fs.existsSync(groupsDataPath)) {
            return res.status(404).json({ error: 'ملف بيانات المجموعات غير موجود' });
        }

        // قراءة الملف وإرساله
        const groupsData = JSON.parse(fs.readFileSync(groupsDataPath, 'utf8'));
        return res.json(groupsData);
    } catch (error) {
        console.error(`Error reading groups JSON for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء قراءة ملف بيانات المجموعات' });
    }
});

// API لتنفيذ عمليات على المجموعات
app.post('/api/groups/:accountName/:operation', async (req, res) => {
    const { accountName, operation } = req.params;
    const { groupId, data } = req.body;

    try {
        // التحقق من وجود الحساب
        if (!connections[accountName]) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // التحقق من اتصال الحساب
        if (connections[accountName].status !== 'connected') {
            return res.status(400).json({ error: 'الحساب غير متصل' });
        }

        if (!groupId) {
            return res.status(400).json({ error: 'معرف المجموعة مطلوب' });
        }

        const client = connections[accountName].client;

        // الحصول على المحادثات
        const chats = await client.getChats();

        // البحث عن المجموعة المطلوبة
        const group = chats.find(chat => chat.isGroup && chat.id._serialized === groupId);

        if (!group) {
            return res.status(404).json({ error: 'المجموعة غير موجودة' });
        }

        // تنفيذ العملية المطلوبة
        switch (operation) {
            case 'get-invite-code':
                try {
                    const inviteCode = await group.getInviteCode();
                    return res.json({
                        success: true,
                        inviteCode: inviteCode,
                        inviteLink: `https://chat.whatsapp.com/${inviteCode}`
                    });
                } catch (error) {
                    console.error(`Error getting invite code: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في الحصول على رمز الدعوة' });
                }

            case 'revoke-invite':
                try {
                    const newInviteCode = await group.revokeInvite();
                    return res.json({
                        success: true,
                        inviteCode: newInviteCode,
                        inviteLink: `https://chat.whatsapp.com/${newInviteCode}`
                    });
                } catch (error) {
                    console.error(`Error revoking invite: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في إلغاء وإعادة إنشاء رمز الدعوة' });
                }

            case 'set-description':
                if (!data || typeof data.description !== 'string') {
                    return res.status(400).json({ error: 'وصف المجموعة مطلوب' });
                }

                try {
                    const success = await group.setDescription(data.description);
                    return res.json({
                        success: success,
                        message: success ? 'تم تحديث وصف المجموعة بنجاح' : 'فشل في تحديث وصف المجموعة'
                    });
                } catch (error) {
                    console.error(`Error setting description: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في تحديث وصف المجموعة' });
                }

            case 'set-subject':
                if (!data || typeof data.subject !== 'string') {
                    return res.status(400).json({ error: 'اسم المجموعة مطلوب' });
                }

                try {
                    const success = await group.setSubject(data.subject);
                    return res.json({
                        success: success,
                        message: success ? 'تم تحديث اسم المجموعة بنجاح' : 'فشل في تحديث اسم المجموعة'
                    });
                } catch (error) {
                    console.error(`Error setting subject: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في تحديث اسم المجموعة' });
                }

            case 'promote-participants':
                if (!data || !Array.isArray(data.participantIds) || data.participantIds.length === 0) {
                    return res.status(400).json({ error: 'معرفات المشاركين مطلوبة' });
                }

                try {
                    const result = await group.promoteParticipants(data.participantIds);
                    return res.json({
                        success: result && result.status === 200,
                        result,
                        message: result && result.status === 200 ? 'تمت ترقية المشاركين بنجاح' : 'فشل في ترقية بعض المشاركين'
                    });
                } catch (error) {
                    console.error(`Error promoting participants: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في ترقية المشاركين' });
                }

            case 'demote-participants':
                if (!data || !Array.isArray(data.participantIds) || data.participantIds.length === 0) {
                    return res.status(400).json({ error: 'معرفات المشاركين مطلوبة' });
                }

                try {
                    const result = await group.demoteParticipants(data.participantIds);
                    return res.json({
                        success: result && result.status === 200,
                        result,
                        message: result && result.status === 200 ? 'تم إلغاء ترقية المشاركين بنجاح' : 'فشل في إلغاء ترقية بعض المشاركين'
                    });
                } catch (error) {
                    console.error(`Error demoting participants: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في إلغاء ترقية المشاركين' });
                }

            case 'add-participants':
                if (!data || !Array.isArray(data.participantIds) || data.participantIds.length === 0) {
                    return res.status(400).json({ error: 'معرفات المشاركين مطلوبة' });
                }

                try {
                    const result = await group.addParticipants(data.participantIds, data.options || {});
                    return res.json({
                        success: true,
                        result,
                        message: 'تمت محاولة إضافة المشاركين'
                    });
                } catch (error) {
                    console.error(`Error adding participants: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في إضافة المشاركين' });
                }

            case 'remove-participants':
                if (!data || !Array.isArray(data.participantIds) || data.participantIds.length === 0) {
                    return res.status(400).json({ error: 'معرفات المشاركين مطلوبة' });
                }

                try {
                    const result = await group.removeParticipants(data.participantIds);
                    return res.json({
                        success: result && result.status === 200,
                        result,
                        message: result && result.status === 200 ? 'تمت إزالة المشاركين بنجاح' : 'فشل في إزالة بعض المشاركين'
                    });
                } catch (error) {
                    console.error(`Error removing participants: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في إزالة المشاركين' });
                }

            case 'leave-group':
                try {
                    await group.leave();
                    return res.json({
                        success: true,
                        message: 'تم مغادرة المجموعة بنجاح'
                    });
                } catch (error) {
                    console.error(`Error leaving group: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في مغادرة المجموعة' });
                }

            case 'approve-membership-requests':
                if (!data || !Array.isArray(data.requesterIds) || data.requesterIds.length === 0) {
                    return res.status(400).json({ error: 'معرفات الطلبات مطلوبة' });
                }

                try {
                    const options = { requesterIds: data.requesterIds };
                    const result = await group.approveGroupMembershipRequests(options);
                    return res.json({
                        success: true,
                        result,
                        message: 'تمت الموافقة على طلبات العضوية'
                    });
                } catch (error) {
                    console.error(`Error approving membership requests: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في الموافقة على طلبات العضوية' });
                }

            case 'reject-membership-requests':
                if (!data || !Array.isArray(data.requesterIds) || data.requesterIds.length === 0) {
                    return res.status(400).json({ error: 'معرفات الطلبات مطلوبة' });
                }

                try {
                    const options = { requesterIds: data.requesterIds };
                    const result = await group.rejectGroupMembershipRequests(options);
                    return res.json({
                        success: true,
                        result,
                        message: 'تم رفض طلبات العضوية'
                    });
                } catch (error) {
                    console.error(`Error rejecting membership requests: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في رفض طلبات العضوية' });
                }

            case 'set-admins-only-messages':
                try {
                    const adminsOnly = data && typeof data.adminsOnly === 'boolean' ? data.adminsOnly : true;
                    const success = await group.setMessagesAdminsOnly(adminsOnly);
                    return res.json({
                        success,
                        message: success
                            ? `تم ${adminsOnly ? 'تفعيل' : 'تعطيل'} خاصية الرسائل للمشرفين فقط`
                            : 'فشل في تغيير إعدادات الرسائل'
                    });
                } catch (error) {
                    console.error(`Error setting messages admins only: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في تغيير إعدادات الرسائل' });
                }

            case 'set-admins-only-settings':
                try {
                    const adminsOnly = data && typeof data.adminsOnly === 'boolean' ? data.adminsOnly : true;
                    const success = await group.setInfoAdminsOnly(adminsOnly);
                    return res.json({
                        success,
                        message: success
                            ? `تم ${adminsOnly ? 'تفعيل' : 'تعطيل'} خاصية تعديل معلومات المجموعة للمشرفين فقط`
                            : 'فشل في تغيير إعدادات المجموعة'
                    });
                } catch (error) {
                    console.error(`Error setting info admins only: ${error.message}`);
                    return res.status(500).json({ error: 'فشل في تغيير إعدادات المجموعة' });
                }

            default:
                return res.status(400).json({ error: 'عملية غير معتمدة' });
        }

    } catch (error) {
        console.error(`Error in group operation ${operation} for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء تنفيذ العملية' });
    }
});

// API لإنشاء مجموعة جديدة
app.post('/api/create-group/:accountName', async (req, res) => {
    const { accountName } = req.params;
    const { title, participants, options } = req.body;

    try {
        // التحقق من وجود الحساب
        if (!connections[accountName]) {
            return res.status(404).json({ error: 'الحساب غير موجود' });
        }

        // التحقق من اتصال الحساب
        if (connections[accountName].status !== 'connected') {
            return res.status(400).json({ error: 'الحساب غير متصل' });
        }

        // التحقق من توفر بيانات المجموعة المطلوبة
        if (!title || typeof title !== 'string') {
            return res.status(400).json({ error: 'عنوان المجموعة مطلوب' });
        }

        if (!participants || !Array.isArray(participants) || participants.length === 0) {
            return res.status(400).json({ error: 'يجب توفير مشارك واحد على الأقل' });
        }

        const client = connections[accountName].client;

        console.log(`Creating group "${title}" for ${accountName} with ${participants.length} participants`);

        // تجهيز الخيارات
        const createOptions = {};
        if (options) {
            if (typeof options.messageTimer === 'number') {
                createOptions.messageTimer = options.messageTimer;
            }
            if (typeof options.parentGroupId === 'string') {
                createOptions.parentGroupId = options.parentGroupId;
            }
            if (typeof options.autoSendInviteV4 === 'boolean') {
                createOptions.autoSendInviteV4 = options.autoSendInviteV4;
            }
            if (typeof options.comment === 'string') {
                createOptions.comment = options.comment;
            }
        }

        // إنشاء المجموعة
        const groupResult = await client.createGroup(title, participants, createOptions);

        // تحليل نتائج إنشاء المجموعة
        const groupData = {
            success: true,
            groupId: groupResult.gid._serialized,
            title: groupResult.title,
            participants: {}
        };

        // تحليل نتائج إضافة المشاركين
        for (const participantId in groupResult.participants) {
            const participant = groupResult.participants[participantId];
            groupData.participants[participantId] = {
                statusCode: participant.statusCode,
                message: participant.message,
                isGroupCreator: participant.isGroupCreator || false,
                isInviteV4Sent: participant.isInviteV4Sent || false
            };
        }

        console.log(`Group "${title}" created successfully with ID: ${groupData.groupId}`);

        // إضافة وصف للمجموعة إذا تم توفيره
        if (options && typeof options.description === 'string') {
            try {
                // الحصول على المجموعة المنشأة حديثًا
                const chats = await client.getChats();
                const newGroup = chats.find(chat => chat.isGroup && chat.id._serialized === groupData.groupId);

                if (newGroup) {
                    await newGroup.setDescription(options.description);
                    groupData.description = options.description;
                    console.log(`Description set for group "${title}"`);
                }
            } catch (descriptionError) {
                console.error(`Error setting description for new group: ${descriptionError.message}`);
                groupData.descriptionError = 'فشل في تعيين وصف المجموعة';
            }
        }

        // محاولة الحصول على رابط دعوة للمجموعة
        try {
            // الحصول على المجموعة المنشأة حديثًا
            const chats = await client.getChats();
            const newGroup = chats.find(chat => chat.isGroup && chat.id._serialized === groupData.groupId);

            if (newGroup) {
                const inviteCode = await newGroup.getInviteCode();
                groupData.inviteCode = inviteCode;
                groupData.inviteLink = `https://chat.whatsapp.com/${inviteCode}`;
                console.log(`Invite code generated for group "${title}": ${inviteCode}`);
            }
        } catch (inviteError) {
            console.error(`Error getting invite code for new group: ${inviteError.message}`);
            groupData.inviteError = 'فشل في الحصول على رابط الدعوة';
        }

        return res.json(groupData);

    } catch (error) {
        console.error(`Error creating group for ${accountName}:`, error);
        return res.status(500).json({ error: 'حدث خطأ أثناء إنشاء المجموعة', message: error.message });
    }
});

/**
 * تحويل معرف LID إلى معرف C.US
 * @param {string} lid معرف LID مثل **********@lid
 * @returns {string|null} معرف C.US مثل <EMAIL> أو null إذا لم يكن المدخل صالحاً
 */
function lidToCus(lid) {
    if (!lid || typeof lid !== 'string') return null;

    // التحقق من أن المعرف lid صالح
    const match = lid.match(/^(\d+)@lid$/);
    if (!match) return null;

    // استخراج الرقم واستبدال @lid بـ @c.us
    const phoneNumber = match[1];
    return `${phoneNumber}@c.us`;
}

/**
 * تحويل معرف C.US إلى معرف LID
 * @param {string} cus معرف C.US مثل <EMAIL>
 * @returns {string|null} معرف LID مثل **********@lid أو null إذا لم يكن المدخل صالحاً
 */
function cusToLid(cus) {
    if (!cus || typeof cus !== 'string') return null;

    // التحقق من أن المعرف c.us صالح
    const match = cus.match(/^(\d+)@c\.us$/);
    if (!match) return null;

    // استخراج الرقم واستبدال @c.us بـ @lid
    const phoneNumber = match[1];
    return `${phoneNumber}@lid`;
}

/**
 * تحويل معرف LID إلى رقم هاتف حقيقي
 * @param {string} lid معرف LID
 * @returns {string} رقم الهاتف الحقيقي
 */
function convertLidToRealPhone(lid) {
    if (!lid) return null;

    // إزالة @lid من المعرف
    const number = lid.replace('@lid', '');

    // التحقق من أن الرقم يبدأ بـ 2 (رقم مصري)
    if (number.startsWith('2')) {
        return number;
    }

    // إذا كان الرقم يبدأ بـ 1 (رقم فيسبوك)
    if (number.startsWith('1')) {
        // تحويل رقم فيسبوك إلى رقم مصري
        // نضيف 2 في البداية ونحذف 1
        return '2' + number.substring(1);
    }

    return number;
}

/**
 * تحويل معرف CUS إلى رقم هاتف حقيقي
 * @param {string} cus معرف CUS
 * @returns {string} رقم الهاتف الحقيقي
 */
function convertCusToRealPhone(cus) {
    if (!cus) return null;

    // إزالة @c.us من المعرف
    const number = cus.replace('@c.us', '');

    // التحقق من أن الرقم يبدأ بـ 2 (رقم مصري)
    if (number.startsWith('2')) {
        return number;
    }

    // إذا كان الرقم يبدأ بـ 1 (رقم فيسبوك)
    if (number.startsWith('1')) {
        // تحويل رقم فيسبوك إلى رقم مصري
        // نضيف 2 في البداية ونحذف 1
        return '2' + number.substring(1);
    }

    return number;
}

// In-memory storage for operations status (for demonstration/basic tracking)
const ongoingOperations = {};

/**
 * Helper function to update operation completion status and store results.
 * This is a basic in-memory implementation.
 * @param {string} operationId - Unique identifier for the operation.
 * @param {number} total - Total number of items processed or expected.
 * @param {number} success - Number of successful items.
 * @param {number} failed - Number of failed items.
 * @param {Array} results - Array of individual item results.
 * @param {string} [errorMessage=null] - General error message for the operation if it failed overall.
 */
function updateOperationCompletionStatus(operationId, total, success, failed, results, errorMessage = null) {
    console.log(`Updating operation ${operationId} status: Total=${total}, Success=${success}, Failed=${failed}`);

    // Ensure the operation exists in storage or create it
    if (!ongoingOperations[operationId]) {
        ongoingOperations[operationId] = {
            id: operationId,
            startTime: Date.now(),
            status: 'processing', // Initial status if not already set
            progress: 0,
            totalExpected: total,
            successCount: 0,
            failCount: 0,
            errors: [],
            results: []
        };
    }

    const operation = ongoingOperations[operationId];

    // Update counts
    operation.successCount = success;
    operation.failCount = failed;

    // Update results (can merge or replace depending on granularity needed)
    // For simplicity, we'll just store the final batch of results provided
    operation.results = results;

    // Determine overall status
    if (errorMessage) {
        operation.status = 'failed';
        operation.error = errorMessage;
        operation.progress = 100; // Consider failed operations as completed for progress bar
    } else if (success + failed >= total && total > 0) { // Operation complete if processed count reaches total
        operation.status = failed > 0 ? 'completed_with_errors' : 'completed';
        operation.progress = 100; // Always 100% when complete
    } else {
        // Calculate progress based on processed items
        const processedCount = success + failed;
        operation.progress = total > 0 ? Math.round((processedCount / total) * 100) : 0;
        // Keep status as 'processing' if not fully completed
        if (operation.status !== 'failed' && operation.status !== 'completed' && operation.status !== 'completed_with_errors') {
            operation.status = 'processing';
        }
    }

    operation.endTime = Date.now();
    operation.duration = operation.endTime - operation.startTime;

    console.log(`Operation ${operationId} updated: Status=${operation.status}, Progress=${operation.progress}%`);
    // console.log('Updated operation details:', operation); // Log full details for debugging
}

// Expose updateOperationCompletionStatus globally or pass it where needed
// For now, we'll just keep it in this file and ensure /api/operation uses it.


// API to get operation status by ID
app.get('/operation/:operationId', (req, res) => {
    const { operationId } = req.params;
    const operation = tempUtils.getOperationData(operationId);

    if (!operation) {
        return res.status(404).json({ error: 'العملية غير موجودة' });
    }

    res.json({
        operation: {
            id: operationId,
            status: operation.status,
            progress: operation.progress,
            successCount: operation.successCount,
            failCount: operation.failCount,
            errors: operation.errors,
            startTime: operation.startTime,
            endTime: operation.endTime
        }
    });
});



// API لإنشاء ملف الإحصائيات الأولي
app.post('/api/initialize-statistics', (req, res) => {
    try {
        const { sessionId, recipients, messageTypes } = req.body;

        if (!sessionId || !recipients || !messageTypes) {
            return res.status(400).json({
                success: false,
                error: 'معاملات مطلوبة مفقودة: sessionId, recipients, messageTypes'
            });
        }

        const statisticsPath = tempUtils.initializeStatisticsFile(sessionId, recipients, messageTypes);

        if (statisticsPath) {
            res.json({
                success: true,
                message: 'تم إنشاء ملف الإحصائيات بنجاح',
                sessionId: sessionId,
                statisticsPath: statisticsPath
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'فشل في إنشاء ملف الإحصائيات'
            });
        }
    } catch (error) {
        console.error('خطأ في إنشاء ملف الإحصائيات:', error);
        res.status(500).json({
            success: false,
            error: 'فشل في إنشاء ملف الإحصائيات',
            details: error.message
        });
    }
});

// API لقراءة ملف الإحصائيات
app.get('/api/get-statistics/:sessionId', (req, res) => {
    try {
        const { sessionId } = req.params;

        if (!sessionId) {
            return res.status(400).json({
                success: false,
                error: 'معرف الجلسة مطلوب'
            });
        }

        const statisticsPath = path.join(__dirname, 'temp', `statistics_${sessionId}.json`);

        if (!fs.existsSync(statisticsPath)) {
            return res.status(404).json({
                success: false,
                error: 'ملف الإحصائيات غير موجود'
            });
        }

        const statisticsData = JSON.parse(fs.readFileSync(statisticsPath, 'utf8'));

        res.json({
            success: true,
            statistics: statisticsData
        });

    } catch (error) {
        console.error('خطأ في قراءة ملف الإحصائيات:', error);
        res.status(500).json({
            success: false,
            error: 'فشل في قراءة ملف الإحصائيات',
            details: error.message
        });
    }
});

// API لتنظيف ملفات العمليات
app.post('/api/cleanup-operations', (req, res) => {
    try {
        const tempDir = path.join(__dirname, 'temp');

        if (!fs.existsSync(tempDir)) {
            return res.json({
                success: true,
                deletedCount: 0,
                message: 'مجلد temp غير موجود'
            });
        }

        const operationFiles = fs.readdirSync(tempDir).filter(file => file.startsWith('op-') && file.endsWith('.json'));
        let deletedCount = 0;

        operationFiles.forEach(file => {
            try {
                const filePath = path.join(tempDir, file);
                fs.unlinkSync(filePath);
                deletedCount++;
            } catch (error) {
                console.error(`خطأ في حذف ملف العملية ${file}:`, error);
            }
        });

        res.json({
            success: true,
            deletedCount: deletedCount,
            message: `تم حذف ${deletedCount} ملف عملية`
        });

    } catch (error) {
        console.error('خطأ في تنظيف ملفات العمليات:', error);
        res.status(500).json({
            success: false,
            error: 'فشل في تنظيف ملفات العمليات',
            details: error.message
        });
    }
});

// API لتحديث ملف الإحصائيات
app.post('/api/update-statistics', (req, res) => {
    try {
        const { sessionId, recipientId, messageType, success, error, operationId } = req.body;

        if (!sessionId || !recipientId || !messageType || success === undefined) {
            return res.status(400).json({
                success: false,
                error: 'معاملات مطلوبة مفقودة: sessionId, recipientId, messageType, success'
            });
        }

        const isCompleted = tempUtils.updateStatisticsFile(sessionId, recipientId, messageType, success, error, operationId);

        res.json({
            success: true,
            message: 'تم تحديث الإحصائيات بنجاح',
            isCompleted: isCompleted
        });
    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
        res.status(500).json({
            success: false,
            error: 'فشل في تحديث الإحصائيات',
            details: error.message
        });
    }
});