<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إرسال رسالة واتساب</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.min.css">
    <!-- Google Fonts - Tajawal -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap">
    <!-- Emoji Picker Element -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/emoji-picker-element@1.18.3/css/emoji-picker-element.css">
    <style>
        body {
            background-color: #f0f2f5;
            font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .header {
            background-color: #128C7E;
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .message-form {
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .recipients-list {
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            max-height: 300px;
            overflow-y: auto;
        }

        .btn-whatsapp {
            background-color: #25D366;
            color: white;
        }

        .btn-whatsapp:hover {
            background-color: #128C7E;
            color: white;
        }

        .back-btn {
            margin-bottom: 20px;
        }

        .recipient-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #f0f0f0;
        }

        .recipient-image {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            margin-left: 10px;
        }

        .recipient-info {
            flex-grow: 1;
        }

        .recipient-name {
            font-weight: bold;
            margin-bottom: 0;
        }

        .recipient-type {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .message-type-selector {
            margin-bottom: 20px;
        }

        .message-type-btn {
            padding: 10px 15px;
            border-radius: 10px;
            margin-right: 5px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #495057;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .message-type-btn.active {
            background-color: #25D366;
            color: white;
            border-color: #25D366;
        }

        .message-type-btn:hover {
            background-color: #e9ecef;
        }

        .message-type-btn.active:hover {
            background-color: #128C7E;
        }

        .file-upload-container {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .file-upload-container:hover {
            border-color: #25D366;
            background-color: #f8f9fa;
        }

        .file-preview {
            margin-top: 15px;
            display: none;
        }

        .file-preview img {
            max-width: 100%;
            max-height: 200px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .file-info {
            margin-top: 10px;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .progress-container {
            margin-top: 20px;
            display: none;
        }

        .progress {
            height: 15px !important;
            border-radius: 10px;
            background-color: #f0f0f0;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }

        .progress-bar {
            border-radius: 10px;
            font-size: 10px;
            line-height: 15px;
            font-weight: bold;
        }

        /* أنماط منتقي الرموز التعبيرية */
        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 10px;
            max-height: 300px;
            overflow-y: auto;
            padding: 10px;
        }

        .emoji-item {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.8em;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.2s ease;
        }

        .emoji-item:hover {
            background-color: #f0f0f0;
            transform: scale(1.1);
        }

        .emoji-category {
            font-size: 0.9rem;
        }

        .emoji-category.active {
            background-color: #25D366;
            color: white;
            border-color: #25D366;
        }

        .recently-used {
            border-top: 1px solid #eee;
            padding-top: 10px;
        }

        #recentEmojis {
            gap: 10px;
        }

        .recent-emoji {
            font-size: 1.5em;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.2s ease;
        }

        .recent-emoji:hover {
            background-color: #f0f0f0;
        }

        /* أنماط منتقي الرموز التعبيرية الجديد */
        #emojiPickerContainer {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1050;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
            padding: 10px;
            display: none;
        }

        .emoji-picker-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1040;
            display: none;
        }

        .emoji-picker-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .emoji-picker-title {
            font-weight: bold;
            font-size: 1.1rem;
            color: #128C7E;
        }

        .emoji-picker-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #6c757d;
        }

        .emoji-picker-close:hover {
            color: #dc3545;
        }

        .recent-emojis-container {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }

        .recent-emojis-title {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .recent-emojis-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .recent-emoji-item {
            font-size: 1.5rem;
            cursor: pointer;
            padding: 3px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .recent-emoji-item:hover {
            background-color: #f0f0f0;
            transform: scale(1.1);
        }

        /* أنماط جدول تحليل الرسائل */
        .message-analytics-table {
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .message-analytics-table h3 {
            color: #128C7E;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .message-analytics-table .table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 0;
        }

        .message-analytics-table .table th {
            background-color: #128C7E;
            color: white;
            font-weight: 500;
            text-align: center;
            vertical-align: middle;
        }

        .message-analytics-table .table td {
            vertical-align: middle;
            text-align: center;
        }

        .message-analytics-table .table tbody {
            max-height: 350px; /* تعديل الارتفاع ليناسب حوالي 10 سجلات */
            overflow-y: auto;
            display: block;
        }

        .message-analytics-table .table {
            display: inline-table;
            width: 100%;
        }

        .message-analytics-table .table thead,
        .message-analytics-table .table tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .message-analytics-table .table thead {
            width: calc(100% - 17px); /* تعديل العرض لمراعاة شريط التمرير */
        }

        .message-analytics-table .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .message-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-block;
            min-width: 100px;
            text-align: center;
        }

        .status-pending {
            background-color: #ffeeba;
            color: #856404;
        }

        .status-sent {
            background-color: #c3e6cb;
            color: #155724;
        }

        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }

        .message-details-btn {
            background-color: #25D366;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .message-details-btn:hover {
            background-color: #128C7E;
        }

        /* تنسيقات منطقة الإحصائيات الحديثة */
        .statistics-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .statistics-header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .header-icon {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .animated-pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .header-text h3 {
            color: white;
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .header-text p {
            color: rgba(255,255,255,0.8);
            margin: 0;
            font-size: 0.9rem;
        }

        .header-actions {
            display: flex;
            gap: 0.5rem;
        }

        .cleanup-btn, .refresh-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .cleanup-btn:hover, .refresh-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .refresh-btn {
            padding: 0.5rem;
            min-width: auto;
        }

        .statistics-content {
            padding: 2rem;
            background: white;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #25D366, #128C7E);
        }

        .stat-card.success::before {
            background: linear-gradient(90deg, #28a745, #20c997);
        }

        .stat-card.failed::before {
            background: linear-gradient(90deg, #dc3545, #fd7e14);
        }

        .stat-card.progress::before {
            background: linear-gradient(90deg, #007bff, #6610f2);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .stat-card {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            background: linear-gradient(135deg, #25D366, #128C7E);
        }

        .stat-card.success .stat-icon {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .stat-card.failed .stat-icon {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
        }

        .stat-card.progress .stat-icon {
            background: linear-gradient(135deg, #007bff, #6610f2);
        }

        .spinning {
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin: 0;
            font-weight: 500;
        }

        .progress-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .progress-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .progress-text {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .progress-bar-container {
            position: relative;
        }

        .progress-bar-modern {
            height: 12px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #25D366, #128C7E);
            border-radius: 10px;
            transition: width 0.5s ease;
            position: relative;
            width: 0%;
        }

        .progress-glow {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .recipients-section {
            background: white;
            border-radius: 15px;
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .section-header h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-controls {
            display: flex;
            gap: 0.5rem;
        }

        .filter-btn {
            background: white;
            border: 1px solid #dee2e6;
            color: #6c757d;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: #25D366;
            color: white;
            border-color: #25D366;
        }

        .recipients-list {
            max-height: 400px;
            overflow-y: auto;
            padding: 1rem;
        }

        .recipient-stat-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border: 1px solid #f0f0f0;
            border-radius: 10px;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .recipient-stat-item:hover {
            background: #f8f9fa;
            border-color: #25D366;
        }

        .recipient-stat-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .recipient-stat-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #25D366, #128C7E);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .recipient-stat-details h6 {
            margin: 0;
            color: #2c3e50;
            font-size: 0.9rem;
        }

        .recipient-stat-details p {
            margin: 0;
            color: #6c757d;
            font-size: 0.8rem;
        }

        .recipient-stat-status {
            display: flex;
            gap: 0.5rem;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .status-badge.success {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.failed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-badge.pending {
            background: #fff3cd;
            color: #856404;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .stats-overview {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 1rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .section-header {
                flex-direction: column;
                align-items: stretch;
            }

            .section-controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><i class="fab fa-whatsapp me-2"></i>إرسال رسالة واتساب</h1>
        </div>
    </div>

    <div class="container">
        <button id="backBtn" class="btn btn-secondary back-btn">
            <i class="fas fa-arrow-right me-2"></i>العودة لتفاصيل الحساب
        </button>

        <div class="row">
            <!-- قائمة المستلمين -->
            <div class="col-md-4">
                <div class="recipients-list">
                    <h3>المستلمون</h3>
                    <hr>
                    <div id="recipientsList">
                        <!-- سيتم إضافة المستلمين هنا بواسطة JavaScript -->
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-users mb-2" style="font-size: 2rem;"></i>
                            <p>لم يتم اختيار أي مستلمين بعد</p>
                        </div>
                    </div>
                </div>

                <!-- تم حذف حاوية شريط التقدم والتقرير -->
            </div>

            <!-- نموذج الرسالة -->
            <div class="col-md-8">
                <div class="message-form">
                    <h3>إرسال رسالة</h3>
                    <hr>

                    <!-- حقل الفاصل الزمني بين الرسائل -->
                    <div class="mb-3">
                        <label for="messageInterval" class="form-label">الفاصل الزمني بين الرسائل (بالثواني)</label>
                        <input type="number" class="form-control" id="messageInterval" min="3" value="3" placeholder="أدخل الفاصل الزمني (لا يقل عن 3 ثوانٍ)">
                        <div class="form-text text-muted">الحد الأدنى للفاصل الزمني هو 3 ثوانٍ</div>
                    </div>
                    <hr>

                    <!-- اختيار نوع الرسالة -->
                    <div class="message-type-selector">
                        <div class="d-flex">
                            <div class="message-type-btn active" data-type="text">
                                <i class="fas fa-comment me-2"></i>نص
                            </div>
                            <div class="message-type-btn" data-type="image">
                                <i class="fas fa-image me-2"></i>صورة
                            </div>
                            <div class="message-type-btn" data-type="file">
                                <i class="fas fa-file me-2"></i>ملف
                            </div>
                            <div class="message-type-btn" data-type="folder">
                                <i class="fas fa-folder me-2"></i>مجلد
                            </div>
                        </div>
                    </div>

                    <!-- نموذج الرسالة النصية -->
                    <div id="textMessageForm">
                        <div class="mb-3">
                            <label for="messageText" class="form-label">نص الرسالة</label>
                            <div id="toolbar" class="mb-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1" data-command="bold" title="عريض"><i class="fas fa-bold"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1" data-command="italic" title="مائل"><i class="fas fa-italic"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1" data-command="strikeThrough" title="يتوسطه خط"><i class="fas fa-strikethrough"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1" data-command="monospace" title="أحادي المسافة"><i class="fas fa-terminal"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1" id="insertNameBtn" title="إدراج اسم المستلم"><i class="fas fa-user"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="emojiBtn" title="إدراج رمز تعبيري"><i class="fas fa-smile"></i></button>
                            </div>
                            <textarea class="form-control" id="messageText" rows="5" placeholder="اكتب رسالتك هنا..."></textarea>
                        </div>
                    </div>

                    <!-- نموذج رسالة الصورة -->
                    <div id="imageMessageForm" style="display: none;">
                        <div class="file-upload-container" id="imageUploadContainer">
                            <i class="fas fa-cloud-upload-alt mb-2" style="font-size: 2rem; color: #25D366;"></i>
                            <p>انقر لاختيار صورة أو اسحب الصورة وأفلتها هنا</p>
                            <input type="file" id="imageInput" accept="image/*" style="display: none;">
                        </div>
                        <div class="file-preview" id="imagePreview">
                            <img id="previewImg" src="" alt="معاينة الصورة">
                            <div class="file-info" id="imageInfo"></div>
                        </div>
                        <div class="mb-3">
                            <label for="imageCaption" class="form-label">تعليق (اختياري)</label>
                            <textarea class="form-control" id="imageCaption" rows="2" placeholder="أضف تعليقًا للصورة..."></textarea>
                        </div>
                    </div>

                    <!-- نموذج رسالة الملف -->
                    <div id="fileMessageForm" style="display: none;">
                        <div class="file-upload-container" id="fileUploadContainer">
                            <i class="fas fa-file-upload mb-2" style="font-size: 2rem; color: #25D366;"></i>
                            <p>انقر لاختيار ملف أو اسحب الملف وأفلته هنا</p>
                            <input type="file" id="fileInput" style="display: none;">
                        </div>
                        <div class="file-preview" id="filePreview">
                            <div class="file-info" id="fileInfo"></div>
                        </div>
                        <div class="mb-3">
                            <label for="fileCaption" class="form-label">تعليق (اختياري)</label>
                            <textarea class="form-control" id="fileCaption" rows="2" placeholder="أضف تعليقًا للملف..."></textarea>
                        </div>
                    </div>

                    <!-- نموذج رسالة المجلد -->
                    <div id="folderMessageForm" style="display: none;">
                        <div class="file-upload-container" id="folderUploadContainer">
                            <i class="fas fa-folder-open mb-2" style="font-size: 2rem; color: #25D366;"></i>
                            <p>انقر لاختيار مجلد يحتوي على ملفات للإرسال</p>
                            <input type="file" id="folderInput" webkitdirectory directory multiple style="display: none;">
                        </div>
                        <div class="file-preview" id="folderPreview">
                            <div class="folder-info" id="folderInfo"></div>
                            <div class="selected-files-list" id="selectedFilesList"></div>
                        </div>
                        <div class="mb-3">
                            <label for="folderCaption" class="form-label">تعليق (اختياري)</label>
                            <textarea class="form-control" id="folderCaption" rows="2" placeholder="أضف تعليقًا للملفات..."></textarea>
                        </div>
                    </div>

                    <!-- زر الإرسال -->
                    <div class="d-grid gap-2">
                        <button id="sendMessageBtn" class="btn btn-whatsapp">
                            <i class="fas fa-paper-plane me-2"></i>إرسال الرسالة
                        </button>
                    </div>

                    <!-- تم حذف مؤشر التقدم -->
                </div>
            </div>
        </div>
    </div>

    <!-- منطقة الإحصائيات الحديثة -->
    <div class="container mt-4" id="statisticsContainer" style="display: none;">
        <div class="statistics-card">
            <div class="statistics-header">
                <div class="header-content">
                    <div class="header-icon">
                        <i class="fas fa-chart-line animated-pulse"></i>
                    </div>
                    <div class="header-text">
                        <h3>إحصائيات الإرسال المباشرة</h3>
                        <p>تحديث فوري لحالة الرسائل</p>
                    </div>
                    <div class="header-actions">
                        <button class="cleanup-btn" onclick="cleanupOperationFiles()" title="تنظيف ملفات العمليات">
                            <i class="fas fa-trash-alt"></i>
                            <span>تنظيف</span>
                        </button>
                        <button class="refresh-btn" onclick="refreshStatistics()" title="تحديث الإحصائيات">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="statistics-content">
                <!-- الإحصائيات العامة -->
                <div class="stats-overview">
                    <div class="stat-card total">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="totalRecipients">0</div>
                            <div class="stat-label">إجمالي المستلمين</div>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="successfulMessages">0</div>
                            <div class="stat-label">رسائل ناجحة</div>
                        </div>
                    </div>

                    <div class="stat-card failed">
                        <div class="stat-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="failedMessages">0</div>
                            <div class="stat-label">رسائل فاشلة</div>
                        </div>
                    </div>

                    <div class="stat-card progress">
                        <div class="stat-icon">
                            <i class="fas fa-hourglass-half spinning"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="progressPercentage">0%</div>
                            <div class="stat-label">نسبة الإنجاز</div>
                        </div>
                    </div>
                </div>

                <!-- شريط التقدم -->
                <div class="progress-section">
                    <div class="progress-header">
                        <span class="progress-title">تقدم الإرسال</span>
                        <span class="progress-text" id="progressText">0 من 0</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar-modern" id="progressBar">
                            <div class="progress-fill" id="progressFill"></div>
                            <div class="progress-glow"></div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل المستلمين -->
                <div class="recipients-section">
                    <div class="section-header">
                        <h4><i class="fas fa-list-ul"></i> تفاصيل المستلمين</h4>
                        <div class="section-controls">
                            <button class="filter-btn active" data-filter="all">الكل</button>
                            <button class="filter-btn" data-filter="success">ناجح</button>
                            <button class="filter-btn" data-filter="failed">فاشل</button>
                            <button class="filter-btn" data-filter="pending">معلق</button>
                        </div>
                    </div>
                    <div class="recipients-list" id="recipientsStatsList">
                        <!-- سيتم ملء هذا القسم ديناميكياً -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول تحليل الرسائل -->
    <div class="container mt-4 mb-5">
        <div class="row">
            <div class="col-12">
                <div class="message-analytics-table">
                    <h3 class="mb-3"><i class="fas fa-chart-line me-2"></i>تحليل حالة الرسائل المرسلة</h3>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="message-analytics-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>جهة الاتصال</th>
                                    <th>نوع الرسالة</th>
                                    <th>وقت الإرسال</th>
                                    <th>حالة الإرسال</th>
                                    <th>التفاصيل</th>
                                </tr>
                            </thead>
                            <tbody id="message-analytics-body">
                                <!-- سيتم إضافة بيانات الرسائل هنا بواسطة JavaScript -->
                                <tr class="text-center text-muted">
                                    <td colspan="6">
                                        <i class="fas fa-info-circle mb-2" style="font-size: 2rem;"></i>
                                        <p>لم يتم إرسال أي رسائل بعد</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.all.min.js"></script>
    <!-- Emoji Picker Element -->
    <script src="https://cdn.jsdelivr.net/npm/emoji-picker-element@1.18.3/index.js" type="module"></script>
    <!-- وظائف إرسال الرسائل -->
    <script src="send-message.js"></script>
    <!-- منتقي الرموز التعبيرية -->
    <div class="emoji-picker-backdrop" id="emojiPickerBackdrop"></div>
    <div id="emojiPickerContainer">
        <div class="emoji-picker-header">
            <div class="emoji-picker-title">اختر رمزًا تعبيريًا</div>
            <button class="emoji-picker-close" id="closeEmojiPicker">&times;</button>
        </div>
        <emoji-picker></emoji-picker>
        <div class="recent-emojis-container">
            <div class="recent-emojis-title">الرموز المستخدمة مؤخرًا</div>
            <div class="recent-emojis-grid" id="recentEmojisGrid"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // الحصول على اسم الحساب والمستلمين من معلمات URL
            const urlParams = new URLSearchParams(window.location.search);
            let accountName = urlParams.get('account');
            const recipientsParam = urlParams.get('recipients');

            console.log('Initial accountName from URL:', accountName);

            // التحقق الفوري من قيمة accountName المسترجعة من الرابط
            if (accountName === '[object HTMLHeadingElement]') {
                console.error('Critical Error: accountName from URL is "[object HTMLHeadingElement]". This indicates an issue with how the link to this page was generated.');
                Swal.fire({
                    title: 'خطأ فادح في الرابط',
                    text: 'اسم الحساب في رابط الصفحة غير صالح. يرجى العودة والمحاولة مرة أخرى من صفحة تفاصيل الحساب. إذا استمرت المشكلة، قد يكون هناك خطأ في النظام.',
                    icon: 'error',
                    confirmButtonText: 'العودة للرئيسية',
                    confirmButtonColor: '#d33',
                    allowOutsideClick: false
                }).then(() => {
                    window.location.href = '/'; // توجيه المستخدم لصفحة آمنة
                });
                return; // إيقاف تحميل باقي الصفحة
            }

            // التحقق من وجود اسم الحساب
            if (!accountName) {
                Swal.fire({
                    title: 'خطأ',
                    text: 'لم يتم تحديد اسم الحساب',
                    icon: 'error',
                    confirmButtonText: 'العودة للرئيسية',
                    confirmButtonColor: '#25D366'
                }).then(() => {
                    window.location.href = '/';
                });
                return;
            }

            // بدء الاستماع لتحديثات حالة الرسائل
            if (window.messageSender && typeof window.messageSender.startMessageStatusUpdates === 'function') {
                window.messageSender.startMessageStatusUpdates(accountName);
            }

            // إضافة مستمع الحدث لزر العودة
            document.getElementById('backBtn').addEventListener('click', function() {
                // إيقاف اتصال SSE عند مغادرة الصفحة
                if (window.messageSender && typeof window.messageSender.stopMessageStatusUpdates === 'function') {
                    window.messageSender.stopMessageStatusUpdates();
                }
                window.location.href = `/account-details.html?account=${accountName}`;
            });

            // تحميل المستلمين
            let recipients = [];
            if (recipientsParam) {
                try {
                    recipients = JSON.parse(decodeURIComponent(recipientsParam));
                    displayRecipients(recipients);
                } catch (error) {
                    console.error('Error parsing recipients:', error);
                    Swal.fire({
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء تحميل المستلمين',
                        icon: 'error',
                        confirmButtonText: 'حسنًا',
                        confirmButtonColor: '#25D366'
                    });
                }
            }

            // إضافة مستمع الحدث لزر الإرسال
            document.getElementById('sendMessageBtn').addEventListener('click', async function() {
                const sendButton = this;
                sendButton.disabled = true; // تعطيل الزر لمنع النقرات المتعددة
                sendButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الإرسال...';

                // تم حذف استدعاء دالة initializeOverallProgress

                try {
                    if (window.messageSender && typeof window.messageSender.sendAllMessageTypes === 'function') {
                        const results = await window.messageSender.sendAllMessageTypes(
                            accountName.toString(),
                            recipients,
                            null // تم حذف دالة التقدم
                        );
                        // تم حذف استدعاء دالة displayOverallReport
                    } else {
                        // استخدام الطريقة القديمة كاحتياط
                        await sendMessage(accountName.toString(), recipients);
                    }
                } catch (error) {
                    console.error('خطأ أثناء عملية الإرسال الكلية:', error);
                    Swal.fire({
                        title: 'خطأ في الإرسال',
                        text: error.message || 'حدث خطأ غير متوقع أثناء محاولة إرسال الرسائل.',
                        icon: 'error',
                        confirmButtonText: 'حسنًا',
                        confirmButtonColor: '#d33'
                    });
                    // تم حذف استدعاء دالة hideOverallProgress
                } finally {
                    sendButton.disabled = false;
                    sendButton.innerHTML = 'إرسال الرسائل <i class="fas fa-paper-plane"></i>';
                }
            });

            // إضافة مستمعات الأحداث لأزرار نوع الرسالة
            const messageTypeBtns = document.querySelectorAll('.message-type-btn');
            messageTypeBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع الأزرار
                    messageTypeBtns.forEach(b => b.classList.remove('active'));
                    // إضافة الفئة النشطة للزر المحدد
                    this.classList.add('active');

                    // إخفاء جميع نماذج الرسائل
                    document.getElementById('textMessageForm').style.display = 'none';
                    document.getElementById('imageMessageForm').style.display = 'none';
                    document.getElementById('fileMessageForm').style.display = 'none';
                    document.getElementById('folderMessageForm').style.display = 'none';

                    // إظهار نموذج الرسالة المناسب
                    const messageType = this.getAttribute('data-type');
                    document.getElementById(`${messageType}MessageForm`).style.display = 'block';
                });
            });

            // إضافة مستمعات الأحداث لمربعات تحميل الملفات
            setupFileUpload('image', 'imageInput', 'imageUploadContainer', 'imagePreview', 'previewImg', 'imageInfo');
            setupFileUpload('file', 'fileInput', 'fileUploadContainer', 'filePreview', null, 'fileInfo');
            setupFolderUpload('folder', 'folderInput', 'folderUploadContainer', 'folderPreview', 'folderInfo', 'selectedFilesList');

            // تم نقل معالج حدث زر إدراج اسم المستلم إلى send-message.js

            // إعداد منتقي الرموز التعبيرية
            setupEmojiPicker();

            // إضافة معالج لإغلاق اتصال SSE عند إغلاق النافذة
            window.addEventListener('beforeunload', function() {
                if (window.messageSender && typeof window.messageSender.stopMessageStatusUpdates === 'function') {
                    window.messageSender.stopMessageStatusUpdates();
                }
            });
        });

        // وظيفة إعداد منتقي الرموز التعبيرية
        function setupEmojiPicker() {
            const emojiBtn = document.getElementById('emojiBtn');
            const emojiPickerContainer = document.getElementById('emojiPickerContainer');
            const emojiPickerBackdrop = document.getElementById('emojiPickerBackdrop');
            const closeEmojiPicker = document.getElementById('closeEmojiPicker');
            const emojiPicker = document.querySelector('emoji-picker');
            const messageText = document.getElementById('messageText');
            const recentEmojisGrid = document.getElementById('recentEmojisGrid');

            // الحصول على الرموز المستخدمة مؤخرًا من التخزين المحلي
            let recentEmojis = JSON.parse(localStorage.getItem('recentEmojis') || '[]');

            // عرض الرموز المستخدمة مؤخرًا
            function displayRecentEmojis() {
                recentEmojisGrid.innerHTML = '';
                recentEmojis.slice(0, 8).forEach(emoji => {
                    const emojiElement = document.createElement('div');
                    emojiElement.className = 'recent-emoji-item';
                    emojiElement.textContent = emoji;
                    emojiElement.addEventListener('click', () => {
                        insertEmoji(emoji);
                    });
                    recentEmojisGrid.appendChild(emojiElement);
                });
            }

            // إضافة رمز إلى قائمة الرموز المستخدمة مؤخرًا
            function addToRecentEmojis(emoji) {
                // إزالة الرمز إذا كان موجودًا بالفعل
                recentEmojis = recentEmojis.filter(e => e !== emoji);

                // إضافة الرمز في بداية المصفوفة
                recentEmojis.unshift(emoji);

                // الاحتفاظ بأحدث 16 رمزًا فقط
                if (recentEmojis.length > 16) {
                    recentEmojis = recentEmojis.slice(0, 16);
                }

                // حفظ الرموز في التخزين المحلي
                localStorage.setItem('recentEmojis', JSON.stringify(recentEmojis));

                // تحديث العرض
                displayRecentEmojis();
            }

            // إدراج رمز تعبيري في صندوق النص
            function insertEmoji(emoji) {
                // الحصول على موضع المؤشر
                const start = messageText.selectionStart;
                const end = messageText.selectionEnd;
                const text = messageText.value;

                // إدراج الرمز في الموضع الحالي
                messageText.value = text.substring(0, start) + emoji + text.substring(end);

                // تحديث موضع المؤشر
                messageText.selectionStart = messageText.selectionEnd = start + emoji.length;

                // تركيز صندوق النص
                messageText.focus();

                // إضافة الرمز إلى قائمز الرموز المستخدمة مؤخرًا
                addToRecentEmojis(emoji);

                // إغلاق منتقي الرموز
                closeEmojiPickerModal();
            }

            // فتح منتقي الرموز
            function openEmojiPickerModal() {
                emojiPickerContainer.style.display = 'block';
                emojiPickerBackdrop.style.display = 'block';
            }

            // إغلاق منتقي الرموز
            function closeEmojiPickerModal() {
                emojiPickerContainer.style.display = 'none';
                emojiPickerBackdrop.style.display = 'none';
            }

            // إضافة مستمع الحدث لزر الرموز التعبيرية
            emojiBtn.addEventListener('click', openEmojiPickerModal);

            // إضافة مستمع الحدث لزر الإغلاق
            closeEmojiPicker.addEventListener('click', closeEmojiPickerModal);

            // إضافة مستمع الحدث للنقر على الخلفية
            emojiPickerBackdrop.addEventListener('click', closeEmojiPickerModal);

            // إضافة مستمع الحدث لاختيار رمز تعبيري
            emojiPicker.addEventListener('emoji-click', event => {
                insertEmoji(event.detail.unicode);
            });

            // عرض الرموز المستخدمة مؤخرًا عند التحميل
            displayRecentEmojis();
        }

        // وظيفة لعرض المستلمين
        function displayRecipients(recipients) {
            const recipientsList = document.getElementById('recipientsList');

            if (!recipients || recipients.length === 0) {
                recipientsList.innerHTML = `
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-users mb-2" style="font-size: 2rem;"></i>
                        <p>لم يتم اختيار أي مستلمين بعد</p>
                    </div>
                `;
                return;
            }

            let html = '';
            recipients.forEach(recipient => {
                html += `
                    <div class="recipient-item">
                        ${recipient.image ?
                            `<img src="${recipient.image}" alt="${recipient.name}" class="recipient-image">` :
                            `<div class="recipient-image d-flex align-items-center justify-content-center bg-light">
                                <i class="${recipient.type === 'contact' ? 'fas fa-user' : 'fas fa-users'}"></i>
                            </div>`
                        }
                        <div class="recipient-info">
                            <p class="recipient-name">${recipient.name}</p>
                            <p class="recipient-type">${recipient.type === 'contact' ? 'جهة اتصال' : 'مجموعة'}</p>
                        </div>
                    </div>
                `;
            });

            recipientsList.innerHTML = html;
        }

        // وظيفة لإعداد تحميل الملفات
        function setupFileUpload(type, inputId, containerId, previewId, imgId, infoId) {
            const input = document.getElementById(inputId);
            const container = document.getElementById(containerId);
            const preview = document.getElementById(previewId);
            const info = document.getElementById(infoId);

            // إضافة مستمع الحدث للنقر على الحاوية
            container.addEventListener('click', function() {
                input.click();
            });

            // إضافة مستمع الحدث لتغيير الملف
            input.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const file = this.files[0];

                    // عرض معلومات الملف
                    info.textContent = `${file.name} (${formatFileSize(file.size)})`;

                    // إذا كان نوع الملف صورة، عرض معاينة الصورة
                    if (type === 'image' && imgId) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            document.getElementById(imgId).src = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }

                    // إظهار معاينة الملف
                    preview.style.display = 'block';
                }
            });

            // إضافة مستمعات الأحداث للسحب والإفلات
            container.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.style.borderColor = '#25D366';
                this.style.backgroundColor = '#f8f9fa';
            });

            container.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.style.borderColor = '#dee2e6';
                this.style.backgroundColor = '';
            });

            container.addEventListener('drop', function(e) {
                e.preventDefault();
                this.style.borderColor = '#dee2e6';
                this.style.backgroundColor = '';

                if (e.dataTransfer.files && e.dataTransfer.files[0]) {
                    input.files = e.dataTransfer.files;

                    // تشغيل حدث التغيير يدويًا
                    const event = new Event('change');
                    input.dispatchEvent(event);
                }
            });
        }

        // وظيفة لتنسيق حجم الملف
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 بايت';
            const k = 1024;
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // وظيفة لإعداد تحميل المجلد
        function setupFolderUpload(type, inputId, containerId, previewId, infoId, filesListId) {
            const input = document.getElementById(inputId);
            const container = document.getElementById(containerId);
            const preview = document.getElementById(previewId);
            const info = document.getElementById(infoId);
            const filesList = document.getElementById(filesListId);

            // إضافة مستمع الحدث للنقر على الحاوية
            container.addEventListener('click', function() {
                input.click();
            });

            // إضافة مستمع الحدث لتغيير الملفات
            input.addEventListener('change', function() {
                if (this.files && this.files.length > 0) {
                    const files = Array.from(this.files);

                    // حساب إجمالي حجم الملفات
                    const totalSize = files.reduce((total, file) => total + file.size, 0);

                    // عرض معلومات المجلد
                    info.textContent = `تم اختيار ${files.length} ملف (${formatFileSize(totalSize)})`;

                    // عرض قائمة الملفات المختارة
                    filesList.innerHTML = '';
                    files.forEach(file => {
                        const fileItem = document.createElement('div');
                        fileItem.className = 'selected-file-item';
                        fileItem.innerHTML = `
                            <i class="fas fa-file me-2"></i>
                            <span class="file-name">${file.name}</span>
                            <span class="file-size">(${formatFileSize(file.size)})</span>
                        `;
                        filesList.appendChild(fileItem);
                    });

                    // إظهار معاينة المجلد
                    preview.style.display = 'block';
                }
            });

            // إضافة مستمعات الأحداث للسحب والإفلات
            container.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.style.borderColor = '#25D366';
                this.style.backgroundColor = '#f8f9fa';
            });

            container.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.style.borderColor = '#dee2e6';
                this.style.backgroundColor = '';
            });

            container.addEventListener('drop', function(e) {
                e.preventDefault();
                this.style.borderColor = '#dee2e6';
                this.style.backgroundColor = '';

                // ملاحظة: السحب والإفلات للمجلدات قد لا يعمل في جميع المتصفحات
                // لذلك نعتمد على النقر لاختيار المجلد
            });
        }

        // وظيفة لإرسال الرسالة
        async function sendMessage(accountName, recipients) {
            // التحقق من وجود المستلمين
            if (!recipients || recipients.length === 0) {
                Swal.fire({
                    title: 'تنبيه',
                    text: 'يجب اختيار مستلم واحد على الأقل',
                    icon: 'warning',
                    confirmButtonText: 'حسنًا',
                    confirmButtonColor: '#25D366'
                });
                return;
            }

            // التأكد من أن اسم الحساب هو نص وليس عنصر HTML
            let accountNameStr = accountName;
            console.log('sendMessage in HTML: Original accountName type:', typeof accountName, 'Value:', accountName);
            if (typeof accountNameStr !== 'string') {
                console.warn('sendMessage in HTML: accountName is not a string, attempting conversion.');
                if (accountNameStr && typeof accountNameStr.toString === 'function') {
                    accountNameStr = accountNameStr.toString().trim();
                    console.log('sendMessage in HTML: Converted accountName to string:', accountNameStr);
                }
            } else {
                accountNameStr = accountNameStr.trim();
            }

            // التحقق مما إذا كان accountNameStr لا يزال كائنًا أو سلسلة غير مرغوب فيها
            if (accountNameStr.startsWith('[object') && accountNameStr.endsWith('Element]')) {
                console.error('sendMessage in HTML: accountName is still an object string representation:', accountNameStr);
                // هنا يجب أن تقرر كيفية التعامل مع هذا الخطأ، ربما عرض رسالة للمستخدم أو استخدام قيمة افتراضية
                Swal.fire({
                    title: 'خطأ فادح',
                    text: 'اسم الحساب غير صالح. يرجى التحقق من الرابط أو إعادة تحميل الصفحة.',
                    icon: 'error',
                    confirmButtonText: 'حسنًا',
                    confirmButtonColor: '#25D366'
                });
                return; // إيقاف التنفيذ
            }

            // الحصول على نوع الرسالة النشط
            const activeType = document.querySelector('.message-type-btn.active').getAttribute('data-type');

            // التحقق من محتوى الرسالة
            let content, caption;
            switch (activeType) {
                case 'text':
                    content = document.getElementById('messageText').value.trim();
                    if (!content) {
                        Swal.fire({
                            title: 'تنبيه',
                            text: 'يرجى كتابة نص الرسالة',
                            icon: 'warning',
                            confirmButtonText: 'حسنًا',
                            confirmButtonColor: '#25D366'
                        });
                        return;
                    }
                    break;
                case 'image':
                    const imageInput = document.getElementById('imageInput');
                    if (!imageInput.files || !imageInput.files[0]) {
                        Swal.fire({
                            title: 'تنبيه',
                            text: 'يرجى اختيار صورة',
                            icon: 'warning',
                            confirmButtonText: 'حسنًا',
                            confirmButtonColor: '#25D366'
                        });
                        return;
                    }
                    content = imageInput.files[0];
                    caption = document.getElementById('imageCaption').value.trim();
                    break;
                case 'file':
                    const fileInput = document.getElementById('fileInput');
                    if (!fileInput.files || !fileInput.files[0]) {
                        Swal.fire({
                            title: 'تنبيه',
                            text: 'يرجى اختيار ملف',
                            icon: 'warning',
                            confirmButtonText: 'حسنًا',
                            confirmButtonColor: '#25D366'
                        });
                        return;
                    }
                    content = fileInput.files[0];
                    caption = document.getElementById('fileCaption').value.trim();
                    break;
                case 'folder':
                    const folderInput = document.getElementById('folderInput');
                    if (!folderInput.files || folderInput.files.length === 0) {
                        Swal.fire({
                            title: 'تنبيه',
                            text: 'يرجى اختيار مجلد يحتوي على ملفات',
                            icon: 'warning',
                            confirmButtonText: 'حسنًا',
                            confirmButtonColor: '#25D366'
                        });
                        return;
                    }
                    content = folderInput.files;
                    caption = document.getElementById('folderCaption').value.trim();
                    break;
            }

            // إظهار مؤشر التقدم وحاوية حالة العملية
            const operationStatusContainer = document.getElementById('operationStatusContainer');
            const progressBarContainer = document.getElementById('progressBarContainer');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            // إظهار العناصر وتعيين القيم الأولية
            if(operationStatusContainer) operationStatusContainer.style.display = 'block';
            if(progressBarContainer) progressBarContainer.style.display = 'flex';
            if(progressText) progressText.style.display = 'block';

            if(progressBar) {
                progressBar.style.width = '0%';
                progressBar.setAttribute('aria-valuenow', 0);
                progressBar.textContent = '0%';
                progressBar.classList.remove('bg-success', 'bg-warning', 'bg-danger');
                progressBar.classList.add('bg-info'); // اللون الأولي
            }
            if(progressText) progressText.textContent = 'جاري تهيئة عملية الإرسال...'; // النص الأولي

            try {
                // تعطيل زر الإرسال أثناء العملية
                const sendButton = document.getElementById('sendMessageBtn');
                sendButton.disabled = true;
                sendButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';

                // التأكد من أن اسم الحساب هو نص وليس عنصر HTML
                let accountNameStr = accountName;
                console.log('send-message.html: النوع الأصلي لاسم الحساب:', typeof accountName, accountName);
                if (accountName && typeof accountName !== 'string') {
                    console.warn('send-message.html: تم استلام اسم حساب غير نصي، محاولة تحويله إلى نص.');
                    accountNameStr = String(accountName).trim();
                    console.log('send-message.html: تم تحويل اسم الحساب إلى:', accountNameStr);
                } else if (typeof accountName === 'string') {
                    accountNameStr = accountName.trim();
                }

                // إرسال الرسالة باستخدام وظيفة WhatsAppMessenger
                // تجهيز محتوى الرسالة حسب النوع
                let messageContent;
                if (activeType === 'text') {
                    messageContent = { type: 'text', text: content }; // إرسال المحتوى الأصلي ليتم معالجته في send-message.js
                } else if (activeType === 'image' || activeType === 'file') {
                    messageContent = { file: content, caption: caption };
                } else if (activeType === 'folder') {
                    // تحويل FileList إلى مصفوفة لسهولة التعامل معها
                    const filesArray = Array.from(content);
                    messageContent = { files: filesArray, caption: caption };
                }

                // تم إزالة دالة تحديث مؤشر التقدم

                const result = await window.messageSender.sendMessageToAll(
                    accountNameStr,
                    recipients,
                    activeType,
                    messageContent
                );

                // عرض رسالة نجاح أو فشل
                if (result.failCount > 0) {
                    // عرض تفاصيل الأخطاء
                    let errorDetails = '';
                    if (result.errors && result.errors.length > 0) {
                        result.errors.forEach(err => {
                            errorDetails += `<p><strong>${err.recipient || 'مستلم'}:</strong> ${err.message || 'خطأ غير معروف'}</p>`;
                        });
                    }

                    Swal.fire({
                        title: 'تم الإرسال مع بعض الأخطاء',
                        html: `تم إرسال الرسالة بنجاح إلى ${result.successCount} من أصل ${result.totalRecipients} مستلم.<br><br><strong>تفاصيل الأخطاء:</strong><br>${errorDetails}`,
                        icon: 'warning',
                        confirmButtonText: 'حسنًا',
                        confirmButtonColor: '#25D366'
                    });
                    // قم بالتمرير إلى جدول التحليلات
                    document.getElementById('message-analytics-table').scrollIntoView({ behavior: 'smooth' });
                } else {
                    Swal.fire({
                        title: 'تم الإرسال بنجاح',
                        text: `تم إرسال الرسالة بنجاح إلى ${result.totalRecipients} مستلم`,
                        icon: 'success',
                        confirmButtonText: 'حسنًا',
                        confirmButtonColor: '#25D366'
                    });
                    // قم بالتمرير إلى جدول التحليلات
                    document.getElementById('message-analytics-table').scrollIntoView({ behavior: 'smooth' });
                }
            } catch (error) {
                console.error('خطأ في إرسال الرسالة:', error);

                // تم إزالة كود تحديث مؤشر التقدم في حالة الخطأ

                Swal.fire({
                    title: 'خطأ',
                    text: `فشل إرسال الرسالة: ${error.message}`,
                    icon: 'error',
                    confirmButtonText: 'حسنًا',
                    confirmButtonColor: '#25D366'
                });
            } finally {
                // إعادة تفعيل زر الإرسال
                const sendButton = document.getElementById('sendMessageBtn');
                sendButton.disabled = false;
                sendButton.innerHTML = '<i class="fas fa-paper-plane me-2"></i>إرسال الرسالة';
            }
        }
    </script>
</body>
</html>

