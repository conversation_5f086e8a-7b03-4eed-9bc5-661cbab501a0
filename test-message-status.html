<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حالة الرسائل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .message-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-sent {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .status-delivered {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-read {
            background-color: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }
        
        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-updated {
            animation: pulse-update 0.8s ease-in-out;
        }
        
        @keyframes pulse-update {
            0% { 
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
            }
            50% { 
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
            }
            100% { 
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
            }
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">اختبار حالة الرسائل</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>حالات الرسائل المختلفة:</h3>
                <div class="mb-3">
                    <span class="message-status status-pending"><i class="fas fa-clock"></i> قيد الإرسال</span>
                </div>
                <div class="mb-3">
                    <span class="message-status status-sent"><i class="fas fa-check"></i> تم الإرسال</span>
                </div>
                <div class="mb-3">
                    <span class="message-status status-delivered"><i class="fas fa-check-double"></i> تم التسليم</span>
                </div>
                <div class="mb-3">
                    <span class="message-status status-read"><i class="fas fa-check-double"></i> تم القراءة</span>
                </div>
                <div class="mb-3">
                    <span class="message-status status-failed"><i class="fas fa-times"></i> فشل الإرسال</span>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>اختبار التحديث المباشر:</h3>
                <div class="mb-3">
                    <span id="testStatus" class="message-status status-pending" data-message-id="test123">
                        <i class="fas fa-clock"></i> قيد الإرسال
                    </span>
                </div>
                
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="updateToSent()">تحديث إلى "تم الإرسال"</button>
                </div>
                <div class="mb-3">
                    <button class="btn btn-success" onclick="updateToDelivered()">تحديث إلى "تم التسليم"</button>
                </div>
                <div class="mb-3">
                    <button class="btn btn-info" onclick="updateToRead()">تحديث إلى "تم القراءة"</button>
                </div>
                <div class="mb-3">
                    <button class="btn btn-danger" onclick="updateToFailed()">تحديث إلى "فشل"</button>
                </div>
                <div class="mb-3">
                    <button class="btn btn-secondary" onclick="resetToPending()">إعادة تعيين</button>
                </div>
            </div>
        </div>
        
        <div class="row mt-5">
            <div class="col-12">
                <h3>اختبار SSE (Server-Sent Events):</h3>
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="startSSE()">بدء اتصال SSE</button>
                    <button class="btn btn-secondary" onclick="stopSSE()">إيقاف اتصال SSE</button>
                </div>
                <div id="sseStatus" class="alert alert-info">
                    لم يتم بدء اتصال SSE بعد
                </div>
                <div id="sseMessages" class="border p-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa;">
                    <small class="text-muted">رسائل SSE ستظهر هنا...</small>
                </div>
            </div>
        </div>
    </div>

    <script>
        let eventSource = null;
        
        // دالة لتحديث عنصر الحالة
        function updateStatusElement(element, status) {
            let statusText = '';
            let statusClass = '';
            let icon = '';
            
            switch (status) {
                case 'pending':
                    statusText = 'قيد الإرسال';
                    statusClass = 'status-pending';
                    icon = '<i class="fas fa-clock"></i>';
                    break;
                case 'sent':
                    statusText = 'تم الإرسال';
                    statusClass = 'status-sent';
                    icon = '<i class="fas fa-check"></i>';
                    break;
                case 'delivered':
                    statusText = 'تم التسليم';
                    statusClass = 'status-delivered';
                    icon = '<i class="fas fa-check-double"></i>';
                    break;
                case 'read':
                    statusText = 'تم القراءة';
                    statusClass = 'status-read';
                    icon = '<i class="fas fa-check-double"></i>';
                    break;
                case 'failed':
                    statusText = 'فشل الإرسال';
                    statusClass = 'status-failed';
                    icon = '<i class="fas fa-times"></i>';
                    break;
            }
            
            element.className = `message-status ${statusClass} status-updated`;
            element.innerHTML = `${icon} ${statusText}`;
        }
        
        // دوال الاختبار
        function updateToSent() {
            const element = document.getElementById('testStatus');
            updateStatusElement(element, 'sent');
        }
        
        function updateToDelivered() {
            const element = document.getElementById('testStatus');
            updateStatusElement(element, 'delivered');
        }
        
        function updateToRead() {
            const element = document.getElementById('testStatus');
            updateStatusElement(element, 'read');
        }
        
        function updateToFailed() {
            const element = document.getElementById('testStatus');
            updateStatusElement(element, 'failed');
        }
        
        function resetToPending() {
            const element = document.getElementById('testStatus');
            updateStatusElement(element, 'pending');
        }
        
        // دوال SSE
        function startSSE() {
            if (eventSource) {
                eventSource.close();
            }
            
            const statusDiv = document.getElementById('sseStatus');
            const messagesDiv = document.getElementById('sseMessages');
            
            statusDiv.textContent = 'جاري الاتصال...';
            statusDiv.className = 'alert alert-warning';
            
            // محاولة الاتصال بـ SSE (سيفشل إذا لم يكن الخادم يعمل)
            eventSource = new EventSource('/api/message-status-updates/test');
            
            eventSource.onopen = function() {
                statusDiv.textContent = 'متصل بنجاح!';
                statusDiv.className = 'alert alert-success';
                addMessage('اتصال SSE تم بنجاح');
            };
            
            eventSource.onerror = function() {
                statusDiv.textContent = 'خطأ في الاتصال - تأكد من تشغيل الخادم';
                statusDiv.className = 'alert alert-danger';
                addMessage('خطأ في اتصال SSE');
            };
            
            eventSource.addEventListener('connected', function(event) {
                const data = JSON.parse(event.data);
                addMessage(`متصل: ${data.message}`);
            });
            
            eventSource.addEventListener('messageStatusUpdate', function(event) {
                const data = JSON.parse(event.data);
                addMessage(`تحديث حالة: ${JSON.stringify(data)}`);
            });
            
            eventSource.addEventListener('heartbeat', function(event) {
                const data = JSON.parse(event.data);
                addMessage(`نبضة: ${new Date(data.timestamp).toLocaleTimeString()}`);
            });
        }
        
        function stopSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                
                const statusDiv = document.getElementById('sseStatus');
                statusDiv.textContent = 'تم قطع الاتصال';
                statusDiv.className = 'alert alert-secondary';
                
                addMessage('تم إغلاق اتصال SSE');
            }
        }
        
        function addMessage(message) {
            const messagesDiv = document.getElementById('sseMessages');
            const time = new Date().toLocaleTimeString();
            messagesDiv.innerHTML += `<div><small class="text-muted">[${time}]</small> ${message}</div>`;
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
    </script>
</body>
</html>
