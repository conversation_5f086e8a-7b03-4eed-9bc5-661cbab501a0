[0526/011724.329:INFO:CONSOLE(162)] "Event handler of 'x-storagemutated-1' event must be added on the initial evaluation of worker script.", source: https://static.whatsapp.net/rsrc.php/v4/yr/r/4MPJ9JTACi3.js (162)
[0526/011724.385:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[0526/011724.385:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[0526/011724.386:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[0526/011724.386:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[0526/011724.387:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[0526/011724.387:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[0526/011724.387:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[0526/011724.387:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[0526/011724.387:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'unload'.", source:  (0)
[0526/011724.388:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[0526/011825.533:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011828.788:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011829.253:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011829.485:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011832.003:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011836.039:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011837.664:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011838.084:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011839.576:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011841.572:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011844.077:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011844.485:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011847.591:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011851.672:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011904.663:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
[0526/011904.759:INFO:CONSOLE(4)] "Uncaught (in promise) Error: getRecipientInfoByMessageId is not a function", source: https://web.whatsapp.com/ (4)
