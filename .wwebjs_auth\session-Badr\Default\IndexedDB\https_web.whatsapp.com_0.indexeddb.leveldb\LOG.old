2025/05/25-23:52:26.271 1f10 Reusing MANIFEST D:\Whatsappnode\.wwebjs_auth\session-Badr\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/05/25-23:52:26.273 1f10 Recovering log #850
2025/05/25-23:52:26.335 1f10 Reusing old log D:\Whatsappnode\.wwebjs_auth\session-Badr\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/000850.log 
2025/05/25-23:52:26.336 1f10 Delete type=2 #846
2025/05/25-23:52:26.336 1f10 Delete type=2 #847
2025/05/25-23:52:26.336 1f10 Delete type=2 #848
2025/05/25-23:52:26.336 1f10 Delete type=2 #851
2025/05/25-23:52:29.374 d94 Level-0 table #858: started
2025/05/25-23:52:29.399 d94 Level-0 table #858: 152737 bytes OK
2025/05/25-23:52:29.403 d94 Delete type=0 #850
2025/05/25-23:52:29.404 d94 Manual compaction at level-0 from '\x00\xb7\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\xb8\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/25-23:52:38.840 1af0 Compacting 1@1 + 4@2 files
2025/05/25-23:52:39.123 1af0 Generated table #859@1: 86093 keys, 2235703 bytes
2025/05/25-23:52:39.303 1af0 Generated table #860@1: 41078 keys, 2226872 bytes
2025/05/25-23:52:39.492 1af0 Generated table #861@1: 59405 keys, 2215184 bytes
2025/05/25-23:52:39.520 1af0 Generated table #862@1: 5897 keys, 96768 bytes
2025/05/25-23:52:39.520 1af0 Compacted 1@1 + 4@2 files => 6774527 bytes
2025/05/25-23:52:39.522 1af0 compacted to: files[ 0 0 4 0 0 0 0 ]
2025/05/25-23:52:39.523 1af0 Delete type=2 #852
2025/05/25-23:52:39.523 1af0 Delete type=2 #853
2025/05/25-23:52:39.523 1af0 Delete type=2 #854
2025/05/25-23:52:39.523 1af0 Delete type=2 #855
2025/05/25-23:52:39.523 1af0 Delete type=2 #858
