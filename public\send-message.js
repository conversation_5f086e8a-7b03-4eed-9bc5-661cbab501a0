/**
 * وظائف إرسال الرسائل باستخدام مكتبة Baileys
 * هذا الملف يحتوي على وظائف لإرسال رسائل نصية وصور وملفات إلى جهات الاتصال المختارة
 */

// وظيفة إرسال رسالة نصية إلى مستلم واحد أو مجموعة من المستلمين
async function sendTextMessage(accountName, recipients, message) {
    try {
        // التأكد من أن اسم الحساب هو نص وليس عنصر HTML
        console.log('sendTextMessage: النوع الأصلي لاسم الحساب:', typeof accountName, accountName);
        if (accountName && typeof accountName !== 'string') {
            console.warn('sendTextMessage: تم استلام اسم حساب غير نصي، محاولة تحويله إلى نص.');
            accountName = String(accountName).trim();
            console.log('sendTextMessage: تم تحويل اسم الحساب إلى:', accountName);
        }

        // التحقق من وجود المستلمين والرسالة
        if (!recipients || recipients.length === 0) {
            throw new Error('لم يتم تحديد المستلمين');
        }

        if (!message || message.trim() === '') {
            throw new Error('الرسالة فارغة');
        }

        // بدء تتبع حالة العملية قبل الإرسال
        const operationId = startOperationStatusTracking(null, recipients, 'نص');

        // الحصول على الفاصل الزمني بين الرسائل
        const messageIntervalElement = document.getElementById('messageInterval');
        let messageInterval = 3; // القيمة الافتراضية 3 ثوانٍ

        if (messageIntervalElement && messageIntervalElement.value) {
            messageInterval = parseInt(messageIntervalElement.value);
            // التأكد من أن الفاصل الزمني لا يقل عن 3 ثوانٍ
            if (messageInterval < 3) {
                messageInterval = 3;
                messageIntervalElement.value = 3;
            }
        }

        console.log(`sendTextMessage: الفاصل الزمني بين الرسائل: ${messageInterval} ثوانٍ`);

        // إرسال طلب إلى الخادم لإرسال الرسالة
        const response = await fetch(`/api/send-message/${accountName}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                recipients: recipients,
                message: {
                    text: message
                },
                operationId: operationId,
                messageInterval: messageInterval
            })
        });

        const result = await response.json();
        console.log('sendTextMessage - results:', result.results);

        if (result.error) {
            // تحديث حالة جميع المستلمين إلى فشل
            recipients.forEach((recipient, index) => {
                updateMessageStatus(operationId, index, 'failed', result.error);
            });
            throw new Error(result.error);
        }

        // إذا كانت النتائج متوفرة، حدث الحالة فوراً
        if (result.results && Array.isArray(result.results) && result.results.length > 0) {
            result.results.forEach((resultItem, index) => {
                // استخدام الحالة المباشرة من نتيجة WhatsApp
                let status = resultItem.status;

                // تحويل رموز الحالة الرقمية إلى نصية
                if (typeof status === 'number') {
                    switch (status) {
                        case 0:
                            status = 'pending';
                            break;
                        case 1:
                            status = 'sent';
                            break;
                        case 2:
                            status = 'delivered';
                            break;
                        case 3:
                            status = 'read';
                            break;
                    }
                }

                // إذا لم تكن الحالة محددة، نستخدم خاصية success
                if (!status) {
                    status = resultItem.success ? 'sent' : 'failed';
                }

                const errorMsg = resultItem.success ? null : (resultItem.error || 'فشل الإرسال');
                console.log('Message result:', {
                    operationId,
                    index,
                    status,
                    errorMsg,
                    fullResult: resultItem
                });
                updateMessageStatus(operationId, index, status, errorMsg, resultItem);
            });
        } else {
            // إذا لم تكن النتائج متوفرة، ابدأ polling للحصول على النتائج
            console.log('No results in initial response, starting polling for operation:', operationId);
            startOperationPolling(operationId);
        }

        return result;
    } catch (error) {
        console.error('خطأ في إرسال الرسالة النصية:', error);
        throw error;
    }
}

// وظيفة إرسال صورة إلى مستلم واحد أو مجموعة من المستلمين
async function sendImageMessage(accountName, recipients, imageFile, caption = '') {
    try {
        // التأكد من أن اسم الحساب هو نص وليس عنصر HTML
        console.log('sendImageMessage: النوع الأصلي لاسم الحساب:', typeof accountName, accountName);
        if (accountName && typeof accountName !== 'string') {
            console.warn('sendImageMessage: تم استلام اسم حساب غير نصي، محاولة تحويله إلى نص.');
            accountName = String(accountName).trim();
            console.log('sendImageMessage: تم تحويل اسم الحساب إلى:', accountName);
        }

        // التحقق من وجود المستلمين والصورة
        if (!recipients || recipients.length === 0) {
            throw new Error('لم يتم تحديد المستلمين');
        }

        if (!imageFile) {
            throw new Error('لم يتم تحديد الصورة');
        }

        // بدء تتبع حالة العملية قبل الإرسال
        const operationId = startOperationStatusTracking(null, recipients, 'صورة');

        // إنشاء FormData لإرسال الملف
        const formData = new FormData();
        formData.append('image', imageFile);
        formData.append('recipients', JSON.stringify(recipients));
        formData.append('caption', caption);
        formData.append('operationId', operationId);

        // الحصول على الفاصل الزمني بين الرسائل
        const messageIntervalElement = document.getElementById('messageInterval');
        let messageInterval = 3; // القيمة الافتراضية 3 ثوانٍ

        if (messageIntervalElement && messageIntervalElement.value) {
            messageInterval = parseInt(messageIntervalElement.value);
            // التأكد من أن الفاصل الزمني لا يقل عن 3 ثوانٍ
            if (messageInterval < 3) {
                messageInterval = 3;
            }
        }

        console.log(`sendImageMessage: الفاصل الزمني بين الرسائل: ${messageInterval} ثوانٍ`);
        formData.append('messageInterval', messageInterval.toString());

        // إرسال طلب إلى الخادم لإرسال الصورة
        const response = await fetch(`/api/send-image/${accountName}`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        console.log('sendTextMessage - results:', result.results);

        if (result.error) {
            // تحديث حالة جميع المستلمين إلى فشل
            recipients.forEach((recipient, index) => {
                updateMessageStatus(operationId, index, 'failed', result.error);
            });
            throw new Error(result.error);
        }

        // تحديث حالة المستلمين بناءً على النتيجة
        if (result.results && Array.isArray(result.results) && result.results.length > 0) {
            result.results.forEach((resultItem, index) => {
                const status = resultItem.success ? 'sent' : 'failed';
                const errorMsg = resultItem.success ? null : (resultItem.error || 'فشل الإرسال');
                updateMessageStatus(operationId, index, status, errorMsg);
            });
        } else {
            // ابدأ polling للحصول على النتائج
            startOperationPolling(operationId);
        }

        return result;
    } catch (error) {
        console.error('خطأ في إرسال الصورة:', error);
        throw error;
    }
}

// وظيفة إرسال مجلد يحتوي على ملفات إلى مستلم واحد أو مجموعة من المستلمين
async function sendFolderFiles(accountName, recipients, files, caption = '') {
    try {
        console.log('sendFolderFiles: Account Name Type:', typeof accountName, accountName);
        if (accountName && typeof accountName !== 'string') {
            console.warn('sendFolderFiles: Non-string account name received, attempting conversion.');
            accountName = String(accountName).trim();
            console.log('sendFolderFiles: Account name converted to:', accountName);
        }

        if (!recipients || recipients.length === 0) {
            throw new Error('لم يتم تحديد المستلمين');
        }

        if (!files || files.length === 0) {
            throw new Error('لم يتم تحديد أي ملفات في المجلد');
        }

        const operationId = startOperationStatusTracking(null, recipients, 'مجلد');
        const formData = new FormData();
        files.forEach((file) => {
            formData.append('files', file);
        });
        formData.append('recipients', JSON.stringify(recipients));
        formData.append('caption', caption);
        formData.append('operationId', operationId);

        // الحصول على الفاصل الزمني بين الرسائل
        const messageIntervalElement = document.getElementById('messageInterval');
        let messageInterval = 3; // القيمة الافتراضية 3 ثوانٍ

        if (messageIntervalElement && messageIntervalElement.value) {
            messageInterval = parseInt(messageIntervalElement.value);
            // التأكد من أن الفاصل الزمني لا يقل عن 3 ثوانٍ
            if (messageInterval < 3) {
                messageInterval = 3;
            }
        }

        console.log(`sendFolderFiles: الفاصل الزمني بين الرسائل: ${messageInterval} ثوانٍ`);
        formData.append('messageInterval', messageInterval.toString());

        const response = await fetch(`/api/send-folder/${accountName}`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        console.log('sendFolderFiles - initial results from server:', result.results);

        if (result.error) {
            //  في حالة وجود خطأ عام من الخادم قبل بدء المعالجة الفردية
            recipients.forEach((_recipient, index) => {
                //  نفترض أن الخطأ ينطبق على جميع المستلمين بشكل مبدئي
                updateMessageStatus(operationId, index, 'failed', result.error);
            });
            throw new Error(result.error);
        }


        // تحديث حالة المستلمين بناءً على النتيجة
        if (result.results && Array.isArray(result.results) && result.results.length > 0) {
            result.results.forEach((resultItem, index) => {
                const status = resultItem.success ? 'sent' : 'failed';
                const errorMsg = resultItem.success ? null : (resultItem.error || 'فشل الإرسال');

                // إضافة معلومات إضافية للمجلدات
                if (resultItem.messageType === 'folder') {
                    console.log(`Folder result for recipient ${index}: ${resultItem.successCount}/${resultItem.successCount + resultItem.failCount} files sent`);
                }

                updateMessageStatus(operationId, index, status, errorMsg , resultItem);
            });
        } else {
            // ابدأ polling للحصول على النتائج
            startOperationPolling(operationId);
        }

        return result;
    } catch (error) {
        console.error('خطأ في إرسال المجلد:', error);
        throw error;
    }
}

// وظيفة إرسال ملف إلى مستلم واحد أو مجموعة من المستلمين
async function sendFileMessage(accountName, recipients, file, caption = '') {
    try {
        // التأكد من أن اسم الحساب هو نص وليس عنصر HTML
        console.log('sendFileMessage: النوع الأصلي لاسم الحساب:', typeof accountName, accountName);
        if (accountName && typeof accountName !== 'string') {
            console.warn('sendFileMessage: تم استلام اسم حساب غير نصي، محاولة تحويله إلى نص.');
            accountName = String(accountName).trim();
            console.log('sendFileMessage: تم تحويل اسم الحساب إلى:', accountName);
        }

        // التحقق من وجود المستلمين والملف
        if (!recipients || recipients.length === 0) {
            throw new Error('لم يتم تحديد المستلمين');
        }

        if (!file) {
            throw new Error('لم يتم تحديد الملف');
        }

        // بدء تتبع حالة العملية قبل الإرسال
        const operationId = startOperationStatusTracking(null, recipients, 'ملف');

        // إنشاء FormData لإرسال الملف
        const formData = new FormData();
        formData.append('file', file);
        formData.append('recipients', JSON.stringify(recipients));
        formData.append('caption', caption);
        formData.append('operationId', operationId);

        // الحصول على الفاصل الزمني بين الرسائل
        const messageIntervalElement = document.getElementById('messageInterval');
        let messageInterval = 3; // القيمة الافتراضية 3 ثوانٍ

        if (messageIntervalElement && messageIntervalElement.value) {
            messageInterval = parseInt(messageIntervalElement.value);
            // التأكد من أن الفاصل الزمني لا يقل عن 3 ثوانٍ
            if (messageInterval < 3) {
                messageInterval = 3;
            }
        }

        console.log(`sendFileMessage: الفاصل الزمني بين الرسائل: ${messageInterval} ثوانٍ`);
        formData.append('messageInterval', messageInterval.toString());

        // إرسال طلب إلى الخادم لإرسال الملف
        const response = await fetch(`/api/send-file/${accountName}`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        console.log('sendTextMessage - results:', result.results);

        if (result.error) {
            // تحديث حالة جميع المستلمين إلى فشل
            recipients.forEach((_recipient, index) => {
                updateMessageStatus(operationId, index, 'failed', result.error);
            });
            throw new Error(result.error);
        }

        // تحديث حالة المستلمين بناءً على النتيجة
        if (result.results && Array.isArray(result.results) && result.results.length > 0) {
            result.results.forEach((resultItem, index) => {
                const status = resultItem.success ? 'sent' : 'failed';
                const errorMsg = resultItem.success ? null : (resultItem.error || 'فشل الإرسال');
                updateMessageStatus(operationId, index, status, errorMsg);
            });
        } else {
            // ابدأ polling للحصول على النتائج
            startOperationPolling(operationId);
        }

        return result;
    } catch (error) {
        console.error('خطأ في إرسال الملف:', error);
        throw error;
    }
}

// وظيفة إرسال رسالة إلى جميع المستلمين المحددين - تم توحيد منطق الإرسال لجميع أنواع الرسائل
async function sendMessageToAll(accountName, recipients, messageType, messageContent, progressCallback, sessionId = null) {
    // التأكد من أن اسم الحساب هو نص وليس عنصر HTML
    console.log('sendMessageToAll: النوع الأصلي لاسم الحساب:', typeof accountName, accountName);
    if (accountName && typeof accountName !== 'string') {
        console.warn('sendMessageToAll: تم استلام اسم حساب غير نصي، محاولة تحويله إلى نص.');
        accountName = String(accountName).trim();
        console.log('sendMessageToAll: تم تحويل اسم الحساب إلى:', accountName);
    }

    // التحقق من وجود المستلمين
    if (!recipients || recipients.length === 0) {
        throw new Error('لم يتم تحديد المستلمين');
    }

    // التحقق من نوع الرسالة ومحتواها
    if (!messageType || !messageContent) {
        throw new Error('نوع الرسالة أو المحتوى غير محدد');
    }

    // الحصول على الفاصل الزمني بين الرسائل
    const messageIntervalElement = document.getElementById('messageInterval');
    let messageInterval = 3; // القيمة الافتراضية 3 ثوانٍ

    if (messageIntervalElement && messageIntervalElement.value) {
        messageInterval = parseInt(messageIntervalElement.value);
        // التأكد من أن الفاصل الزمني لا يقل عن 3 ثوانٍ
        if (messageInterval < 3) {
            messageInterval = 3;
            messageIntervalElement.value = 3;
        }
    }

    console.log('sendMessageToAll: الفاصل الزمني بين الرسائل:', messageInterval, 'ثوانٍ');

    // تسجيل المستلمين ومحتوى الرسالة الأولي
    console.log('sendMessageToAll: المستلمون:', JSON.stringify(recipients));
    console.log('sendMessageToAll: نوع الرسالة:', messageType);
    console.log('sendMessageToAll: محتوى الرسالة الأولي:', JSON.stringify(messageContent));

    // إنشاء كائن لتخزين نتائج الإرسال
    const results = {
        totalRecipients: recipients.length,
        successCount: 0,
        failCount: 0,
        errors: [],
        operationId: null
    };

    // بدء تتبع حالة العملية قبل الإرسال
    const operationId = startOperationStatusTracking(null, recipients, messageType);
    results.operationId = operationId;

    // تم حذف استدعاء دالة startOperationPolling

    try {
        let result;
        let endpoint;
        let requestOptions;

        // تحضير الطلب حسب نوع الرسالة
        if (messageType === 'text') {
            // معالجة الرسائل النصية
            let currentMessageText = messageContent.text;
            console.log('sendMessageToAll: نص الرسالة قبل الاستبدال:', currentMessageText);

            // إذا كان هناك مستلم واحد فقط، استبدل العنصر النائب باسم المستلم
            if (recipients && recipients.length === 1 && recipients[0] && recipients[0].name) {
                console.log('sendMessageToAll: اسم المستلم للاستبدال:', recipients[0].name);
                currentMessageText = currentMessageText.replace(/{recipient_name}/g, recipients[0].name);
                console.log('sendMessageToAll: نص الرسالة بعد الاستبدال:', currentMessageText);
            }

            endpoint = `/api/send-message/${accountName}`;
            requestOptions = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    recipients: recipients,
                    message: {
                        text: currentMessageText
                    },
                    operationId: operationId,
                    messageInterval: messageInterval, // إرسال قيمة الفاصل الزمني إلى الخادم
                    sessionId: sessionId // إرسال sessionId لتحديث الإحصائيات
                })
            };
        } else if (messageType === 'image') {
            // معالجة الصور
            const formData = new FormData();
            formData.append('image', messageContent.file);
            formData.append('recipients', JSON.stringify(recipients));
            formData.append('caption', messageContent.caption || '');
            formData.append('operationId', operationId);
            formData.append('messageInterval', messageInterval); // إضافة الفاصل الزمني
            if (sessionId) formData.append('sessionId', sessionId); // إضافة sessionId

            endpoint = `/api/send-image/${accountName}`;
            requestOptions = {
                method: 'POST',
                body: formData
            };
        } else if (messageType === 'file') {
            // معالجة الملفات
            const formData = new FormData();
            formData.append('file', messageContent.file);
            formData.append('recipients', JSON.stringify(recipients));
            formData.append('caption', messageContent.caption || '');
            formData.append('operationId', operationId);
            formData.append('messageInterval', messageInterval); // إضافة الفاصل الزمني
            if (sessionId) formData.append('sessionId', sessionId); // إضافة sessionId

            endpoint = `/api/send-file/${accountName}`;
            requestOptions = {
                method: 'POST',
                body: formData
            };
        } else if (messageType === 'folder') {
            // معالجة المجلدات
            const formData = new FormData();
            Array.from(messageContent.files).forEach((file) => {
                formData.append('files', file);
            });
            formData.append('recipients', JSON.stringify(recipients));
            formData.append('caption', messageContent.caption || '');
            formData.append('operationId', operationId);
            if (sessionId) formData.append('sessionId', sessionId); // إضافة sessionId

            endpoint = `/api/send-folder/${accountName}`;
            requestOptions = {
                method: 'POST',
                body: formData
            };
        } else {
            throw new Error(`نوع الرسالة غير مدعوم: ${messageType}`);
        }

        // إرسال الطلب الموحد
        try {
            console.log(`sendMessageToAll: إرسال طلب ${messageType} إلى ${endpoint}`);
            const response = await fetch(endpoint, requestOptions);
            result = await response.json();

            if (result.error) {
                throw new Error(result.error);
            }

            // تحديث حالة المستلمين بناءً على النتيجة
            if (result.results && Array.isArray(result.results) && result.results.length > 0) {
                result.results.forEach((resultItem, index) => {
                    const status = resultItem.success ? 'sent' : 'failed';
                    const errorMsg = resultItem.success ? null : (resultItem.error || 'فشل الإرسال');
                    updateMessageStatus(operationId, index, status, errorMsg);
                });

                // تحديث إحصائيات النجاح والفشل
                results.successCount = result.results.filter(r => r.success).length;
                results.failCount = result.results.filter(r => !r.success).length;

                // تجميع الأخطاء
                result.results.forEach((resultItem, index) => {
                    if (!resultItem.success && resultItem.error) {
                        results.errors.push({
                            recipient: recipients[index],
                            message: resultItem.error
                        });
                    }
                });
            } else {
                // ابدأ polling للحصول على النتائج
                startOperationPolling(operationId);
            }

            // تم حذف استدعاء دالة pollOperationStatus

            return result;
        } catch (error) {
            console.error(`فشل إرسال ${messageType} دفعة واحدة:`, error);
            // لا تحاول الإرسال الفردي، فقط أظهر الخطأ لجميع المستلمين
            recipients.forEach((_recipient, index) => {
                updateMessageStatus(operationId, index, 'failed', error.message);
            });
            throw error;
        }
    } catch (error) {
        console.error('خطأ عام في إرسال الرسائل:', error);
        // تحديث حالة جميع المستلمين إلى فشل في حالة حدوث خطأ عام
        recipients.forEach((_recipient, index) => {
            updateMessageStatus(operationId, index, 'failed', error.message);
        });

        // تحديث النتائج قبل إعادتها
        results.successCount = 0;
        results.failCount = recipients.length;
        results.errors.push({
            message: error.message || 'خطأ غير معروف'
        });

        return results;
    }
}

/**
 * وظيفة متابعة حالة العملية
 * تقوم بالاستعلام عن حالة العملية بشكل دوري وتحديث واجهة المستخدم
 * @param {string} operationId - معرف العملية
 * @param {Array} recipients - قائمة المستلمين
 * @param {string} messageType - نوع الرسالة (نص، صورة، ملف)
 */
function startOperationStatusTracking(operationId, recipients = [], messageType = 'نص') {
    // التحقق من وجود جدول التحليز
    const analyticsTable = document.getElementById('message-analytics-table');
    const analyticsBody = document.getElementById('message-analytics-body');

    if (!analyticsTable || !analyticsBody) {
        console.error('لم يتم العثور على جدول تحليل الرسائل');
        return;
    }

    // إزالة رسالة "لم يتم إرسال أي رسائل بعد" إذا كانت موجودة
    if (analyticsBody.querySelector('.text-center.text-muted')) {
        analyticsBody.innerHTML = '';
    }

    // إنشاء معرف فريد للعملية إذا لم يتم توفيره
    if (!operationId) {
        operationId = 'op-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
    }

    // تخزين بيانات العملية في كائن عام
    if (!window.messageOperations) {
        window.messageOperations = {};
    }
    // إضافة جميع المستلمين دفعة واحدة
    window.messageOperations[operationId] = {
        id: operationId,
        startTime: new Date(),
        status: 'pending',
        progress: 0,
        successCount: 0,
        failCount: 0,
        errors: [],
        recipients: recipients.map((recipient, index) => ({
            index: index,
            rowId: `msg-row-${operationId}-${index}`,
            name: recipient.name || recipient.number || 'غير معروف',
            status: 'pending',
            error: null
        }))
    };
    // إضافة صفوف في الجدول لكل مستلم
    recipients.forEach((recipient, index) => {
        const rowId = `msg-row-${operationId}-${index}`;
        const currentTime = new Date().toLocaleTimeString('ar-SA');
        const recipientName = recipient.name || recipient.number || 'غير معروف';

        // الحالة الأولية تبدأ بـ "قيد الإرسال" وستتحدث بناءً على نتائج الإرسال
        const initialStatus = 'قيد الإرسال';
        const initialStatusClass = 'status-pending';
        // إنشاء صف جديد في الجدول
        const newRow = document.createElement('tr');
        newRow.id = rowId;
        newRow.innerHTML = `
            <td>${index + 1}</td>
            <td>${recipientName}</td>
            <td>${messageType}</td>
            <td>${currentTime}</td>
            <td class="message-status-cell">
                <span class="message-status ${initialStatusClass}">${initialStatus}</span>
            </td>
            <td>
                <button class="message-details-btn" onclick="showMessageDetails('${operationId}', ${index})">
                    <i class="fas fa-info-circle"></i> التفاصيل
                </button>
            </td>
        `;
        analyticsBody.appendChild(newRow);
    });
    return operationId;
}

/**
 * تحديث حالة رسالة في جدول التحليز
 * @param {string} operationId - معرف العملية
 * @param {number} recipientIndex - مؤشر المستلم
 * @param {string} status - حالة الرسالة (pending, sent, delivered, read, failed)
 * @param {string} errorMessage - رسالة الخطأ (اختياري)
 * @param {Object} result - نتيجة الإرسال من WhatsApp (اختياري)
 */
function updateMessageStatus(operationId, recipientIndex, status, errorMessage = null, result = null) {
    if (!window.messageOperations || !window.messageOperations[operationId]) {
        return;
    }

    const operation = window.messageOperations[operationId];
    const recipient = operation.recipients[recipientIndex];
    if (!recipient) return;

    // تحويل الحالة الرقمية إلى نصية إذا كانت رقمية
    let whatsappStatus = status;
    if (result && typeof result.status === 'number') {
        switch (result.status) {
            case 0:
                whatsappStatus = 'pending';
                break;
            case 1:
                whatsappStatus = 'sent';
                break;
            case 2:
                whatsappStatus = 'delivered';
                break;
            case 3:
                whatsappStatus = 'read';
                break;
            default:
                whatsappStatus = status;
        }
    }

    // تحديث حالة المستلم
    recipient.status = whatsappStatus;
    recipient.error = errorMessage;

    const successStatuses = ['sent', 'delivered', 'read', 'played'];
    const failureStatuses = ['failed', 'error'];

    // تحديث العدادات
    if (successStatuses.includes(whatsappStatus)) {
        if (!recipient.counted) {
            operation.successCount++;
            recipient.counted = true;
        }
    } else if (failureStatuses.includes(whatsappStatus)) {
        if (!recipient.counted) {
            operation.failCount++;
            recipient.counted = true;
        }
        if (errorMessage) {
            operation.errors.push({ recipient: recipient.name, message: errorMessage });
        }
    }

    // تحديث واجهة المستخدم
    const row = document.getElementById(recipient.rowId);
    if (row) {
        const statusCell = row.querySelector('.message-status-cell');
        if (statusCell) {
            let statusText = '';
            let statusClass = '';
            let icon = '';

            console.log('Updating message status:', {
                whatsappStatus,
                result,
                errorMessage
            });

            // تحديد النص والفئة حسب الحالة
            switch (whatsappStatus) {
                case 'pending':
                    statusText = 'قيد الإرسال';
                    statusClass = 'status-pending';
                    icon = '<i class="fas fa-clock"></i>';
                    break;
                case 'sent':
                    statusText = 'تم الإرسال';
                    statusClass = 'status-sent';
                    icon = '<i class="fas fa-check"></i>';
                    break;
                case 'delivered':
                case 'received':
                    statusText = 'تم التسليم';
                    statusClass = 'status-delivered';
                    icon = '<i class="fas fa-check-double"></i>';
                    break;
                case 'read':
                case 'played':
                    statusText = 'تم القراءة';
                    statusClass = 'status-read';
                    icon = '<i class="fas fa-check-double"></i>';
                    break;
                case 'failed':
                case 'error':
                    statusText = errorMessage || 'فشل الإرسال';
                    statusClass = 'status-failed';
                    icon = '<i class="fas fa-times"></i>';
                    break;
                default:
                    // إذا لم تكن هناك حالة محددة، تحقق من وجود خطأ
                    if (errorMessage) {
                        statusText = errorMessage;
                        statusClass = 'status-failed';
                        icon = '<i class="fas fa-times"></i>';
                    } else {
                        statusText = whatsappStatus || 'غير معروف';
                        statusClass = 'status-unknown';
                        icon = '<i class="fas fa-question"></i>';
                    }
                    break;
            }

            // إنشاء وتحديث عنصر الحالة
            const statusElement = document.createElement('span');
            statusElement.className = `message-status ${statusClass} status-updated`;
            statusElement.innerHTML = `${icon} ${statusText}`;

            // إضافة البيانات المتعلقة بالرسالة
            if (result) {
                // إضافة معرف الرسالة كسمة data
                if (result.messageId) {
                    statusElement.dataset.messageId = result.messageId;
                    console.log(`Message ID saved to element: ${result.messageId}`);
                }

                // إضافة timestamp كتلميح
                if (result.timestamp) {
                    const date = new Date(result.timestamp * 1000);
                    statusElement.title = `آخر تحديث: ${date.toLocaleString('ar-SA')}`;
                }
            }

            // حذف عنصر الحالة القديم وإضافة الجديد
            const oldStatus = statusCell.querySelector('.message-status');
            if (oldStatus) {
                oldStatus.remove();
            }
            statusCell.appendChild(statusElement);

            // تحديث رسالة الخطأ إذا كانت موجودة
            const errorDisplay = row.querySelector('.message-error');
            if (errorDisplay) {
                errorDisplay.textContent = errorMessage || '';
                errorDisplay.style.display = errorMessage ? 'block' : 'none';
            }
        }
    }
    // تحديث شريط التقدم بناءً على عدد المستلمين فقط
    setTimeout(() => {
        const processedRecipients = operation.recipients.filter(r => r.status === 'sent' || r.status === 'failed').length;
        const totalRecipients = operation.recipients.length;

        // تحديث شريط التقدم
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const progressBarContainer = document.getElementById('progressBarContainer');

        if (progressBar && progressText && progressBarContainer) {
            const percent = totalRecipients > 0 ? Math.round((processedRecipients / totalRecipients) * 100) : 0;
            progressBar.style.width = `${percent}%`;
            progressBar.setAttribute('aria-valuenow', percent);
            progressBar.textContent = `${percent}%`;
            progressText.textContent = `جاري المعالجة: ${processedRecipients} من ${totalRecipients} مستلم`;
            progressBarContainer.style.display = 'flex'; // استخدام flex بدلاً من block
            progressText.style.display = 'block';
        }

        // عند اكتمال جميع المستلمين، أظهر التحليل النهائي
        if (processedRecipients === totalRecipients) {
            displayOperationReport({
                recipientsCount: totalRecipients,
                successCount: operation.successCount,
                failureCount: operation.failCount,
                results: operation.recipients.map(r => ({ success: r.status === 'sent', error: r.error })),
            });
             if (progressText) {
                progressText.textContent = `اكتملت المعالجة: ${processedRecipients} من ${totalRecipients} مستلم`;
            }
        }
    }, 150);
}

/**
 * عرض تفاصيل الرسالة
 * @param {string} operationId - معرف العملية
 * @param {number} recipientIndex - مؤشر المستلم
 */
function showMessageDetails(operationId, recipientIndex) {
    if (!window.messageOperations || !window.messageOperations[operationId]) {
        Swal.fire({
            title: 'خطأ',
            text: 'لم يتم العثور على معلومات العملية',
            icon: 'error'
        });
        return;
    }

    const operation = window.messageOperations[operationId];
    const recipient = operation.recipients.find(r => r.index === recipientIndex);

    if (!recipient) {
        Swal.fire({
            title: 'خطأ',
            text: 'لم يتم العثور على معلومات المستلم',
            icon: 'error'
        });
        return;
    }

    let statusText = 'قيد الإرسال';
    let statusIcon = 'info';

    if (recipient.status === 'sent') {
        statusText = 'تم الإرسال بنجاح';
        statusIcon = 'success';
    } else if (recipient.status === 'failed') {
        statusText = 'فشل الإرسال';
        statusIcon = 'error';
    }

    let detailsHTML = `
        <div class="message-details">
            <p><strong>المستلم:</strong> ${recipient.name}</p>
            <p><strong>وقت البدء:</strong> ${operation.startTime.toLocaleString('ar-SA')}</p>
            <p><strong>الحالة:</strong> ${statusText}</p>
    `;

    if (recipient.error) {
        detailsHTML += `<p><strong>رسالة الخطأ:</strong> ${recipient.error}</p>`;
    }

    detailsHTML += '</div>';

    Swal.fire({
        title: 'تفاصيل الرسالة',
        html: detailsHTML,
        icon: statusIcon,
        confirmButtonText: 'إغلاق',
        confirmButtonColor: '#25D366'
    });
}

    // دالة للاستعلام عن حالة العملية
    const checkOperationStatus = async (operationId) => {
        try {
            const response = await fetch(`/api/operation/${operationId}`);
            const data = await response.json();

            if (!data || data.error) {
                throw new Error(data.error || 'حدث خطأ أثناء الاستعلام عن حالة العملية');
            }

            // تحديث واجهة المستخدم بحالة العملية
            updateOperationUI(operationId, data);

            // ذا لم تكتمل العملية بعد، استمر في الاستعلام
            if (data.status !== 'completed' && data.status !== 'failed') {
                setTimeout(() => checkOperationStatus(operationId), 2000); // استعلام كل 2 ثانية
            }
        } catch (error) {
            console.error('خطأ في الاستعلام عن حالة العملية:', error);
            // تحديث واجهة المستخدم بالخطأ
            const operationElement = document.getElementById(`operation-${operationId}`);
            if (operationElement) {
                const statusElement = operationElement.querySelector('.operation-status');
                if (statusElement) {
                    statusElement.textContent = 'فشل الاستعلام';
                    statusElement.className = 'operation-status failed';
                }

                const errorsElement = operationElement.querySelector('.operation-errors');
                if (errorsElement) {
                    errorsElement.textContent = `خطأ: ${error.message}`;
                }
            }
        }
    };

    // دالة لتحديث واجهة المستخدم بحالة العملية
    const updateOperationUI = (operationId, data) => {
        const operationElement = document.getElementById(`operation-${operationId}`);
        if (!operationElement) {
            console.warn(`لم يتم العثور على عنصر UI للعملية ${operationId}`);
            return;
        }

        // تحديث حالة العملية
        const statusElement = operationElement.querySelector('.operation-status');
        if (statusElement) {
            let statusText = 'جاري التنفيذ...';
            let statusClass = '';

            switch (data.status) {
                case 'completed':
                    statusText = 'اكتملت';
                    statusClass = 'completed';
                    break;
                case 'failed':
                    statusText = 'فشلت';
                    statusClass = 'failed';
                    break;
                case 'processing':
                    statusText = 'جاري التنفيذ...';
                    statusClass = 'processing';
                    break;
                case 'cancelled':
                    statusText = 'تم الإلغاء';
                    statusClass = 'cancelled';
                    break;
                default:
                    statusText = data.status || 'جاري التنفيذ...';
            }

            statusElement.textContent = statusText;
            statusElement.className = `operation-status ${statusClass}`;
        }

        // تحديث شريط التقدم
        const totalRecipients = data.recipientsCount || 0;
        if (totalRecipients > 0) {
            const processedRecipients = (data.successCount || 0) + (data.failureCount || 0);
            const progressPercent = Math.round((processedRecipients / totalRecipients) * 100);
            const progressFill = operationElement.querySelector('.progress-fill');
            const progressText = operationElement.querySelector('.progress-text');

            if (progressFill) progressFill.style.width = `${progressPercent}%`;
            if (progressText) progressText.textContent = `${progressPercent}%`;
        }

        // تحديث عدد النجاحات والفشل
        const successCount = operationElement.querySelector('.success-count');
        const failCount = operationElement.querySelector('.fail-count');

        if (successCount) successCount.textContent = `نجاح: ${data.successCount || 0}`;
        if (failCount) failCount.textContent = `فشل: ${data.failureCount || 0}`;

        // عرض الأخطاء إن وجدت
        const errorsElement = operationElement.querySelector('.operation-errors');
        if (errorsElement) {
            // مسح الأخطاء السابقة
            errorsElement.innerHTML = '';

            // إضافة الخطأ العام إذا وجد
            if (data.error) {
                const errorHeaderItem = document.createElement('div');
                errorHeaderItem.className = 'error-header';
                errorHeaderItem.textContent = 'خطأ عام:';
                errorsElement.appendChild(errorHeaderItem);

                const errorItem = document.createElement('div');
                errorItem.className = 'error-message';
                errorItem.textContent = data.error;
                errorsElement.appendChild(errorItem);
            }

            // إضافة أخطاء النتائج الفردية
            if (data.results && Array.isArray(data.results)) {
                const failedResults = data.results.filter(result => !result.success && result.error);

                if (failedResults.length > 0) {
                    if (data.error) { // إضافة فاصل إذا كان هناك خطأ عام سابق
                        const divider = document.createElement('hr');
                        divider.className = 'error-divider';
                        errorsElement.appendChild(divider);
                    }

                    const failedHeader = document.createElement('div');
                    failedHeader.className = 'failed-header';
                    failedHeader.textContent = 'أخطاء فردية:';
                    errorsElement.appendChild(failedHeader);

                    // عرض أول 5 أخطاء فقط لتجنب طول الصفحة
                    const maxErrorsToShow = 5;
                    const errorsToShow = failedResults.slice(0, maxErrorsToShow);

                    errorsToShow.forEach(result => {
                        const errorItem = document.createElement('div');
                        errorItem.className = 'error-item';
                        const recipientName = result.recipient || 'مستلم مجهول';
                        errorItem.textContent = `${recipientName}: ${result.error || 'خطأ غير معروف'}`;
                        errorsElement.appendChild(errorItem);
                    });

                    // إضافة إشارة لوجود المزيد من الأخطاء
                    if (failedResults.length > maxErrorsToShow) {
                        const moreErrors = document.createElement('div');
                        moreErrors.className = 'more-errors';
                        moreErrors.textContent = `+ ${failedResults.length - maxErrorsToShow} أخطاء أخرى`;
                        errorsElement.appendChild(moreErrors);
                    }
                }
            }
        }

        // إضافة زر إغلاق إذا اكتملت العملية
        if ((data.status === 'completed' || data.status === 'failed' || data.status === 'cancelled') &&
            !operationElement.querySelector('.close-button')) {
            const closeButton = document.createElement('span');
            closeButton.className = 'close-button';
            closeButton.textContent = '×';
            closeButton.onclick = () => {
                operationElement.remove();
                // إذا لم يعد هناك عمليات، إخفاء الحاوية
                const statusContainer = document.getElementById('operations-status-container');
                if (statusContainer && statusContainer.querySelectorAll('.operation-item').length === 0) {
                    statusContainer.style.display = 'none';
                }
            };
            const headerElement = operationElement.querySelector('.operation-header');
            if (headerElement) {
                headerElement.appendChild(closeButton);
            }
        }
    };

// دالة لبدء polling للحصول على نتائج العملية
function startOperationPolling(operationId) {
    console.log(`Starting polling for operation: ${operationId}`);

    const pollInterval = setInterval(async () => {
        try {
            const response = await fetch(`/api/operation/${operationId}`);
            const operationData = await response.json();

            if (operationData) {
                console.log(`Polling operation ${operationId} - Status: ${operationData.status}, Processed: ${operationData.processedCount}/${operationData.totalCount}`);

                if (operationData.results && Array.isArray(operationData.results)) {
                    console.log(`Polling results for operation ${operationId}:`, operationData.results);

                    // تحديث حالة كل مستلم بناءً على النتائج
                    // الآن جميع أنواع الرسائل تستخدم نفس الهيكل الموحد
                    operationData.results.forEach((resultItem, index) => {
                        const status = resultItem.success ? 'sent' : 'failed';
                        const errorMsg = resultItem.success ? null : (resultItem.error || 'فشل الإرسال');

                        // إضافة معلومات إضافية للمجلدات
                        if (resultItem.messageType === 'folder') {
                            console.log(`Folder result for recipient ${index}: ${resultItem.successCount}/${resultItem.successCount + resultItem.failCount} files sent`);
                        }

                        updateMessageStatus(operationId, index, status, errorMsg, resultItem);
                    });
                }

                // إيقاف polling إذا اكتملت العملية
                if (operationData.status === 'completed' || operationData.status === 'failed') {
                    clearInterval(pollInterval);
                    console.log(`Polling stopped for operation ${operationId} - Status: ${operationData.status}`);
                }
            }
        } catch (error) {
            console.error(`Error polling operation ${operationId}:`, error);
            // لا نوقف polling في حالة الخطأ، قد يكون خطأ مؤقت
        }
    }, 3000); // poll كل 3 ثواني (أطول للمجلدات)

    // إيقاف polling بعد 10 دقائق كحد أقصى (أطول للمجلدات)
    setTimeout(() => {
        clearInterval(pollInterval);
        console.log(`Polling timeout for operation ${operationId}`);
    }, 600000);
}

// متغير لتخزين اتصال SSE
let messageStatusEventSource = null;

// دالة لبدء الاستماع لتحديثات حالة الرسائل
function startMessageStatusUpdates(accountName) {
    // إغلاق الاتصال السابق إن وجد
    if (messageStatusEventSource) {
        messageStatusEventSource.close();
    }

    console.log(`Starting SSE connection for message status updates: ${accountName}`);

    // إنشاء اتصال SSE جديد
    messageStatusEventSource = new EventSource(`/api/message-status-updates/${accountName}`);

    // معالج الاتصال الناجح
    messageStatusEventSource.addEventListener('connected', (event) => {
        const data = JSON.parse(event.data);
        console.log('SSE Connected:', data);
    });

    // معالج تحديثات حالة الرسائل
    messageStatusEventSource.addEventListener('messageStatusUpdate', (event) => {
        const statusUpdate = JSON.parse(event.data);
        console.log('Message status update received:', statusUpdate);

        // تحديث حالة الرسالة في الواجهة
        if (statusUpdate.operationId && statusUpdate.recipientIndex !== undefined) {
            updateMessageStatus(
                statusUpdate.operationId,
                statusUpdate.recipientIndex,
                statusUpdate.status,
                null, // لا توجد رسالة خطأ
                statusUpdate // تمرير كائل التحديث الكامل
            );
        } else {
            // إذا لم يكن هناك operationId، ابحث عن العنصر بمعرف الرسالة
            if (statusUpdate.messageId) {
                const statusElements = document.querySelectorAll('.message-status[data-message-id]');
                statusElements.forEach(element => {
                    if (element.dataset.messageId === statusUpdate.messageId) {
                        // تحديث العنصر مباشرة
                        updateStatusElement(element, statusUpdate.status);
                    }
                });
            }
        }
    });

    // معالج الأخطاء
    messageStatusEventSource.addEventListener('error', (event) => {
        console.error('SSE Error:', event);
        // إعادة المحاولة بعد 5 ثوان
        setTimeout(() => {
            if (messageStatusEventSource.readyState === EventSource.CLOSED) {
                startMessageStatusUpdates(accountName);
            }
        }, 5000);
    });

    // معالج heartbeat
    messageStatusEventSource.addEventListener('heartbeat', (event) => {
        const data = JSON.parse(event.data);
        console.log('SSE Heartbeat:', data.timestamp);
    });
}

// دالة لتحديث عنصر الحالة مباشرة
function updateStatusElement(statusElement, status) {
    let statusText = '';
    let statusClass = '';
    let icon = '';

    // تحديد النص والفئة حسب الحالة
    switch (status) {
        case 'pending':
            statusText = 'قيد الإرسال';
            statusClass = 'status-pending';
            icon = '<i class="fas fa-clock"></i>';
            break;
        case 'sent':
            statusText = 'تم الإرسال';
            statusClass = 'status-sent';
            icon = '<i class="fas fa-check"></i>';
            break;
        case 'delivered':
        case 'received':
            statusText = 'تم التسليم';
            statusClass = 'status-delivered';
            icon = '<i class="fas fa-check-double"></i>';
            break;
        case 'read':
        case 'played':
            statusText = 'تم القراءة';
            statusClass = 'status-read';
            icon = '<i class="fas fa-check-double"></i>';
            break;
        case 'failed':
        case 'error':
            statusText = 'فشل الإرسال';
            statusClass = 'status-failed';
            icon = '<i class="fas fa-times"></i>';
            break;
        default:
            statusText = status || 'غير معروف';
            statusClass = 'status-unknown';
            icon = '<i class="fas fa-question"></i>';
            break;
    }

    // تحديث العنصر
    statusElement.className = `message-status ${statusClass} status-updated`;
    statusElement.innerHTML = `${icon} ${statusText}`;

    console.log(`Status element updated: ${status} -> ${statusText}`);
}

// دالة لإيقاف الاستماع لتحديثات حالة الرسائل
function stopMessageStatusUpdates() {
    if (messageStatusEventSource) {
        messageStatusEventSource.close();
        messageStatusEventSource = null;
        console.log('SSE connection closed');
    }
}

// تصدير الوظائف لاستخدامها في ملفات أخرى
window.messageSender = {
    sendTextMessage,
    sendImageMessage,
    sendFileMessage,
    sendFolderFiles,
    sendMessageToAll,
    sendAllMessageTypes, // إضافة الدالة الجديدة
    startOperationStatusTracking,
    updateMessageStatus,
    updateOperationUI,
    checkOperationStatus,
    startMessageStatusUpdates,
    stopMessageStatusUpdates,
    startOperationPolling
    // تم حذف دوال شريط التقدم
};


document.addEventListener('DOMContentLoaded', () => {
    const messageText = document.getElementById('messageText');
    const toolbar = document.getElementById('toolbar');
    const insertNameBtn = document.getElementById('insertNameBtn');
    const emojiBtn = document.getElementById('emojiBtn');

    if (toolbar && messageText) {
        toolbar.addEventListener('click', (event) => {
            const target = event.target.closest('button');
            if (!target) return;

            const command = target.dataset.command;
            if (command) {
                // لتطبيق التنسيقات، واتساب يستخدم رموز خاصة
                // *bold*, _italic_, ~strikethrough~, ```monospace```
                let prefix = '';
                let suffix = '';
                switch (command) {
                    case 'bold':
                        prefix = '*';
                        suffix = '*';
                        break;
                    case 'italic':
                        prefix = '_';
                        suffix = '_';
                        break;
                    case 'strikeThrough':
                        prefix = '~';
                        suffix = '~';
                        break;
                    case 'monospace':
                        prefix = '```';
                        suffix = '```';
                        break;
                }

                const start = messageText.selectionStart;
                const end = messageText.selectionEnd;
                const selectedText = messageText.value.substring(start, end);
                const textBefore = messageText.value.substring(0, start);
                const textAfter = messageText.value.substring(end);

                messageText.value = textBefore + prefix + selectedText + suffix + textAfter;
                messageText.focus();
                // ضبط المؤشر بعد النص المدرج
                messageText.setSelectionRange(start + prefix.length, end + prefix.length);
            }
        });
    }

    if (insertNameBtn && messageText) {
        insertNameBtn.addEventListener('click', () => {
            // افتراضيًا، سيتم إدراج "اسم_المستلم"
            // يمكنك تعديل هذا لاحقًا لجلب الاسم الفعلي إذا كان متاحًا
            const recipientNamePlaceholder = '{recipient_name}';
            const start = messageText.selectionStart;
            const end = messageText.selectionEnd;
            const textBefore = messageText.value.substring(0, start);
            const textAfter = messageText.value.substring(end);
            messageText.value = textBefore + recipientNamePlaceholder + textAfter;
            messageText.focus();
            messageText.setSelectionRange(start + recipientNamePlaceholder.length, start + recipientNamePlaceholder.length);
        });
    }

    if (emojiBtn && messageText) {
        let emojiModalInstance = null; // تعريف المتغير هنا
        const emojiPickerContainer = document.getElementById('emojiPickerContainer');

        emojiBtn.addEventListener('click', () => {
            if (!emojiModalInstance) { // تهيئة النافذة المنبثقة عند أول نقرة
                const modalElement = document.getElementById('emojiModal');
                if (modalElement) {
                    emojiModalInstance = new bootstrap.Modal(modalElement);
                } else {
                    console.error('Emoji modal element not found');
                    return;
                }
            }
            emojiModalInstance.show();
        });

        if (emojiPickerContainer) {
            emojiPickerContainer.addEventListener('click', (event) => {
                const target = event.target.closest('.emoji-option');
                if (target && target.dataset.emoji) {
                    const emoji = target.dataset.emoji;
                    const start = messageText.selectionStart;
                    const end = messageText.selectionEnd;
                    const textBefore = messageText.value.substring(0, start);
                    const textAfter = messageText.value.substring(end);
                    messageText.value = textBefore + emoji + textAfter;
                    messageText.focus();
                    messageText.setSelectionRange(start + emoji.length, start + emoji.length);
                    if (emojiModalInstance) {
                        emojiModalInstance.hide();
                    }
                }
            });
        }
    }

    // تحميل اسم الحساب والمستلمين من معلمات URL
    const urlParams = new URLSearchParams(window.location.search);
    const accountName = urlParams.get('account');
    const recipientsParam = urlParams.get('recipients');
    let recipients = [];
    if (recipientsParam) {
        try {
            recipients = JSON.parse(decodeURIComponent(recipientsParam));
        } catch (e) {
            console.error('خطأ في تحليل المستلمين:', e);
            // يمكنك عرض رسالة خطأ للمستخدم هنا
        }
    }

    // عرض اسم الحساب والمستلمين في الصفحة
    const accountNameDisplay = document.getElementById('accountNameDisplay');
    const recipientsDisplay = document.getElementById('recipientsDisplay');

    if (accountNameDisplay && accountName) {
        accountNameDisplay.textContent = decodeURIComponent(accountName);
    }

    if (recipientsDisplay && recipients.length > 0) {
        const recipientNames = recipients.map(r => r.name || r.id.split('@')[0]);
        recipientsDisplay.textContent = recipientNames.join(', ');
    } else if (recipientsDisplay) {
        recipientsDisplay.textContent = 'غير محدد';
    }
});
// إضافة أنماط CSS للتأثير البصري عند تحديث حالة الرسالة
const styleElement = document.createElement('style');
styleElement.textContent = `
    .status-updated {
        animation: highlight-status 1s ease-in-out;
    }

    @keyframes highlight-status {
        0% { background-color: transparent; }
        50% { background-color: rgba(255, 255, 0, 0.3); }
        100% { background-color: transparent; }
    }

    .message-status {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.9em;
        display: inline-flex;
        align-items: center;
        gap: 5px;
        min-width: 100px;
        justify-content: center;
    }

    .status-sent {
        background-color: #e3f2fd;
        color: #1976d2;
        border: 1px solid #bbdefb;
    }

    .status-delivered {
        background-color: #e8f5e9;
        color: #2e7d32;
        border: 1px solid #c8e6c9;
    }

    .status-read {
        background-color: #e8f5e9;
        color: #1b5e20;
        border: 1px solid #a5d6a7;
    }

    .status-played {
        background-color: #f3e5f5;
        color: #7b1fa2;
        border: 1px solid #e1bee7;
    }

    .status-failed {
        background-color: #ffebee;
        color: #c62828;
        border: 1px solid #ffcdd2;
    }

    .status-pending {
        background-color: #fff3e0;
        color: #ef6c00;
        border: 1px solid #ffe0b2;
    }

    .status-queued {
        background-color: #f3e5f5;
        color: #6a1b9a;
        border: 1px solid #e1bee7;
    }

    .status-unknown {
        background-color: #f5f5f5;
        color: #616161;
        border: 1px solid #e0e0e0;
    }

    .message-status i {
        font-size: 0.9em;
    }

    .status-failed i {
        color: #c62828;
    }

    .status-sent i, .status-delivered i, .status-read i {
        color: #2e7d32;
    }

    .status-pending i {
        color: #ef6c00;
    }

    /* تأثير التحديث المباشر */
    .status-updated {
        animation: pulse-update 0.8s ease-in-out;
    }

    @keyframes pulse-update {
        0% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
        }
        100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
        }
    }
`;
document.head.appendChild(styleElement);


// وظيفة إرسال جميع أنواع الرسائل المتاحة بالترتيب لكل مستلم على حدة
async function sendAllMessageTypes(accountName, recipients, overallProgressCallback) {
    // إنشاء معرف جلسة فريد
    const sessionId = 'session-' + Date.now() + '-' + Math.floor(Math.random() * 1E9);

    // تهيئة نتائج الإرسال الإجمالية
    const overallResults = {
        successCount: 0,
        failCount: 0,
        errors: [],
        detailedResults: []
    };

    // تهيئة المتغيرات الأخرى
    const results = {
        successCount: 0,
        failCount: 0,
        errors: [],
        detailedResults: []
    };

    // قائمة أنواع الرسائل المتاحة
    const messageTypes = [];

    // التحقق من وجود رسالة نصية
    const messageText = document.getElementById('messageText').value.trim();
    if (messageText) {
        messageTypes.push({
            type: 'text',
            content: { text: messageText }
        });
    }

    // التحقق من وجود صورة
    const imageInput = document.getElementById('imageInput');
    if (imageInput && imageInput.files && imageInput.files[0]) {
        messageTypes.push({
            type: 'image',
            content: {
                file: imageInput.files[0],
                caption: document.getElementById('imageCaption').value.trim()
            }
        });
    }

    // التحقق من وجود ملف
    const fileInput = document.getElementById('fileInput');
    if (fileInput && fileInput.files && fileInput.files[0]) {
        messageTypes.push({
            type: 'file',
            content: {
                file: fileInput.files[0],
                caption: document.getElementById('fileCaption').value.trim()
            }
        });
    }

    // التحقق من وجود مجلد
    const folderInput = document.getElementById('folderInput');
    if (folderInput && folderInput.files && folderInput.files.length > 0) {
        messageTypes.push({
            type: 'folder',
            content: {
                files: folderInput.files,
                caption: document.getElementById('folderCaption').value.trim()
            }
        });
    }

    // التحقق من وجود أي نوع من الرسائل
    if (messageTypes.length === 0) {
        throw new Error('لم يتم تحديد أي نوع من الرسائل للإرسال');
    }

    console.log('sendAllMessageTypes: أنواع الرسائل المتاحة للإرسال:', messageTypes.map(m => m.type));

    // الحصول على الفاصل الزمني بين الرسائل
    const messageIntervalElement = document.getElementById('messageInterval');
    let messageInterval = 3; // القيمة الافتراضية 3 ثوانٍ

    if (messageIntervalElement && messageIntervalElement.value) {
        messageInterval = parseInt(messageIntervalElement.value);
        // التأكد من أن الفاصل الزمني لا يقل عن 3 ثوانٍ
        if (messageInterval < 3) {
            messageInterval = 3;
            messageIntervalElement.value = 3;
        }
    }

    console.log('sendAllMessageTypes: الفاصل الزمني بين الرسائل والمستلمين:', messageInterval, 'ثوانٍ');

    // إنشاء ملف الإحصائيات الأولي
    const messageTypeNames = messageTypes.map(mt => mt.type);
    try {
        const response = await fetch('/api/initialize-statistics', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sessionId: sessionId,
                recipients: recipients,
                messageTypes: messageTypeNames
            })
        });

        if (response.ok) {
            console.log('📊 تم إنشاء ملف الإحصائيات الأولي بنجاح');

            // إظهار منطقة الإحصائيات وبدء التحديث
            showStatisticsArea(sessionId);
        } else {
            console.error('❌ فشل في إنشاء ملف الإحصائيات الأولي');
        }
    } catch (error) {
        console.error('❌ خطأ في إنشاء ملف الإحصائيات:', error);
    }

    // إرسال جميع أنواع الرسائل لكل مستلم على حدة مع تطبيق الفاصل الزمني بين المستلمين
    for (let recipientIndex = 0; recipientIndex < recipients.length; recipientIndex++) {
        const currentRecipient = recipients[recipientIndex];
        console.log(`sendAllMessageTypes: جاري إرسال الرسائل للمستلم ${currentRecipient.name || currentRecipient.id} (${recipientIndex+1}/${recipients.length})`);

        // إنشاء كائن لتخزين نتائج الإرسال لهذا المستلم
        const recipientResults = {
            successCount: 0,
            failCount: 0,
            errors: []
        };

        // إرسال كل نوع من أنواع الرسائل بالترتيب لهذا المستلم
        for (let i = 0; i < messageTypes.length; i++) {
            const messageType = messageTypes[i];
            console.log(`sendAllMessageTypes: جاري إرسال الرسالة نوع ${messageType.type} (${i+1}/${messageTypes.length}) للمستلم ${currentRecipient.name || currentRecipient.id}`);

            try {
                // استدعاء دالة إرسال الرسائل حسب النوع لمستلم واحد فقط
                // لا نمرر progressCallback الفردي هنا لأننا سنتعامل مع التقدم بشكل إجمالي
                const result = await window.messageSender.sendMessageToAll(
                    accountName,
                    [currentRecipient], // إرسال لمستلم واحد فقط
                    messageType.type,
                    messageType.content,
                    null, // progressCallback
                    sessionId // تمرير sessionId لتحديث الإحصائيات
                );

                // تحديث النتائج لهذا المستلم
                if (result) {
                    recipientResults.successCount += result.successCount || 0;
                    recipientResults.failCount += result.failCount || 0;

                    // إضافة الأخطاء إلى قائمة الأخطاء لهذا المستلم
                    if (result.errors && result.errors.length > 0) {
                        result.errors.forEach(error => {
                            recipientResults.errors.push({
                                ...error,
                                messageType: messageType.type
                            });
                        });
                    }

                    // حفظ معرف العملية الأخيرة للمتابعة
                    if (result.operationId) {
                        results.operationId = result.operationId;
                    }
                }

                // إضافة تأخير بين أنواع الرسائل المختلفة لنفس المستلم إذا لم تكن هذه الرسالة الأخيرة
                if (i < messageTypes.length - 1) {
                    console.log(`sendAllMessageTypes: انتظار ${messageInterval} ثوانٍ قبل إرسال النوع التالي من الرسائل للمستلم الحالي...`);
                    await new Promise(resolve => setTimeout(resolve, messageInterval * 1000));
                }
            } catch (error) {
                console.error(`فشل إرسال الرسالة نوع ${messageType.type} للمستلم ${currentRecipient.name || currentRecipient.id}:`, error);
                recipientResults.failCount++;
                recipientResults.errors.push({
                    messageType: messageType.type,
                    message: error.message
                });
            }
        }

        // تحديث النتائج الإجمالية بعد إرسال جميع أنواع الرسائل لهذا المستلم
        results.successCount += recipientResults.successCount;
        results.failCount += recipientResults.failCount;

        // إضافة أخطاء هذا المستلم إلى قائمة الأخطاء الإجمالية
        if (recipientResults.errors.length > 0) {
            recipientResults.errors.forEach(error => {
                results.errors.push({
                    ...error,
                    recipient: currentRecipient
                });
            });
        }

        // تحديث التقدم الإجمالي بعد معالجة كل مستلم
        if (typeof overallProgressCallback === 'function') {
            overallProgressCallback({
                processed: recipientIndex + 1,
                total: recipients.length,
                currentRecipientName: currentRecipient.name || currentRecipient.id
            });
        }

        // تجميع نتائج هذا المستلم للتقرير الإجمالي
        overallResults.detailedResults.push({
            recipientId: currentRecipient.id,
            recipientName: currentRecipient.name,
            successMessagesForRecipient: recipientResults.successCount,
            failedMessagesForRecipient: recipientResults.failCount,
            totalMessagesForRecipient: messageTypes.length, // عدد أنواع الرسائل التي تم محاولة إرسالها لهذا المستلم
            errors: recipientResults.errors
        });
        overallResults.successCount += recipientResults.successCount; // هذا قد يحسب عدد الرسائل الناجحة وليس المستلمين
        overallResults.failCount += recipientResults.failCount; // هذا قد يحسب عدد الرسائل الفاشلة وليس المستلمين

        // إضافة تأخير بين المستلمين إذا لم يكن هذا المستلم الأخير
        if (recipientIndex < recipients.length - 1) {
            console.log(`sendAllMessageTypes: انتظار ${messageInterval} ثوانٍ قبل الانتقال للمستلم التالي...`);
            await new Promise(resolve => setTimeout(resolve, messageInterval * 1000));
        }
    }

    // تحديث عدد المستلمين الناجحين والفاشلين بناءً على detailedResults
    overallResults.successCount = overallResults.detailedResults.filter(r => r.failedMessagesForRecipient === 0).length;
    overallResults.failCount = overallResults.detailedResults.filter(r => r.failedMessagesForRecipient > 0).length;

    console.log('sendAllMessageTypes: اكتمل إرسال جميع أنواع الرسائل لجميع المستلمين. النتائج الإجمالية:', overallResults);

    // حساب إجمالي عدد الرسائل المرسلة
    const totalMessagesSent = overallResults.detailedResults.reduce((total, recipient) => {
        return total + recipient.successfulMessagesForRecipient;
    }, 0);

    const totalMessagesAttempted = overallResults.detailedResults.reduce((total, recipient) => {
        return total + recipient.successfulMessagesForRecipient + recipient.failedMessagesForRecipient;
    }, 0);

    // عرض الإحصائيات الإجمالية في الـ console
    console.log(`\n🎉 ===== ملخص العملية الإجمالية =====`);
    console.log(`📨 إجمالي الرسائل المرسلة: ${totalMessagesSent} من ${totalMessagesAttempted} رسالة`);
    console.log(`👥 إجمالي المستلمين: ${recipients.length} مستلم`);
    console.log(`✅ المستلمين الذين تم الإرسال إليهم بنجاح: ${overallResults.successCount}`);
    console.log(`❌ المستلمين الذين فشل الإرسال إليهم: ${overallResults.failCount}`);
    console.log(`📊 معدل النجاح: ${((totalMessagesSent / totalMessagesAttempted) * 100).toFixed(1)}%`);
    console.log(`=======================================\n`);

    console.log(`✅ تم الانتهاء من إرسال جميع الرسائل. الإحصائيات النهائية ستظهر تلقائياً عند اكتمال آخر رسالة.`);

    return overallResults; // إرجاع النتائج الإجمالية المجمعة
}

// ===== وظائف منطقة الإحصائيات الحديثة =====

let currentSessionId = null;
let statisticsUpdateInterval = null;

/**
 * إظهار منطقة الإحصائيات وبدء التحديث
 */
function showStatisticsArea(sessionId) {
    currentSessionId = sessionId;
    const statisticsContainer = document.getElementById('statisticsContainer');
    if (statisticsContainer) {
        statisticsContainer.style.display = 'block';
        statisticsContainer.scrollIntoView({ behavior: 'smooth' });

        // بدء تحديث الإحصائيات كل ثانيتين
        startStatisticsUpdates();
    }
}

/**
 * بدء تحديث الإحصائيات التلقائي
 */
function startStatisticsUpdates() {
    if (statisticsUpdateInterval) {
        clearInterval(statisticsUpdateInterval);
    }

    // تحديث فوري
    updateStatisticsDisplay();

    // تحديث كل ثانيتين
    statisticsUpdateInterval = setInterval(() => {
        updateStatisticsDisplay();
    }, 2000);
}

/**
 * إيقاف تحديث الإحصائيات التلقائي
 */
function stopStatisticsUpdates() {
    if (statisticsUpdateInterval) {
        clearInterval(statisticsUpdateInterval);
        statisticsUpdateInterval = null;
    }
}

/**
 * تحديث عرض الإحصائيات من ملف الإحصائيات
 */
async function updateStatisticsDisplay() {
    if (!currentSessionId) return;

    try {
        const response = await fetch(`/api/get-statistics/${currentSessionId}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.statistics) {
                displayStatistics(data.statistics);
            }
        }
    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
    }
}

/**
 * عرض الإحصائيات في الواجهة
 */
function displayStatistics(stats) {
    // تحديث الإحصائيات العامة
    updateElement('totalRecipients', stats.totalRecipients || 0);
    updateElement('successfulMessages', stats.totalSuccessfulMessages || 0);
    updateElement('failedMessages', stats.totalFailedMessages || 0);

    // حساب نسبة الإنجاز
    const totalExpected = stats.expectedTotalMessages || 0;
    const totalCompleted = Object.values(stats.recipients || {}).reduce((sum, recipient) => {
        return sum + (recipient.completedMessages || 0);
    }, 0);

    const progressPercentage = totalExpected > 0 ? Math.round((totalCompleted / totalExpected) * 100) : 0;
    updateElement('progressPercentage', `${progressPercentage}%`);
    updateElement('progressText', `${totalCompleted} من ${totalExpected}`);

    // تحديث شريط التقدم
    const progressFill = document.getElementById('progressFill');
    if (progressFill) {
        progressFill.style.width = `${progressPercentage}%`;
    }

    // تحديث قائمة المستلمين
    displayRecipientsStats(stats.recipients || {});

    // إيقاف التحديث إذا اكتملت جميع الرسائل
    if (stats.status === 'completed') {
        stopStatisticsUpdates();

        // إضافة تأثير الاكتمال
        const progressIcon = document.querySelector('.progress .stat-icon i');
        if (progressIcon) {
            progressIcon.className = 'fas fa-check-circle';
            progressIcon.parentElement.parentElement.classList.add('completed');
        }
    }
}

/**
 * عرض إحصائيات المستلمين
 */
function displayRecipientsStats(recipients) {
    const recipientsList = document.getElementById('recipientsStatsList');
    if (!recipientsList) return;

    const recipientsArray = Object.entries(recipients);

    if (recipientsArray.length === 0) {
        recipientsList.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="fas fa-users mb-2" style="font-size: 2rem;"></i>
                <p>لا توجد بيانات إحصائية بعد</p>
            </div>
        `;
        return;
    }

    let html = '';
    recipientsArray.forEach(([recipientId, recipientData]) => {
        const name = recipientData.name || recipientId.replace('@c.us', '');
        const avatar = name.charAt(0).toUpperCase();

        // حساب حالة المستلم
        const totalMessages = recipientData.totalMessages || 0;
        const completedMessages = recipientData.completedMessages || 0;
        const successfulMessages = recipientData.successfulMessages || 0;
        const failedMessages = recipientData.failedMessages || 0;

        let status = 'pending';
        if (completedMessages === totalMessages) {
            status = successfulMessages > 0 ? 'success' : 'failed';
        }

        const progressPercent = totalMessages > 0 ? Math.round((completedMessages / totalMessages) * 100) : 0;

        html += `
            <div class="recipient-stat-item" data-status="${status}">
                <div class="recipient-stat-info">
                    <div class="recipient-stat-avatar">${avatar}</div>
                    <div class="recipient-stat-details">
                        <h6>${name}</h6>
                        <p>${completedMessages}/${totalMessages} رسالة (${progressPercent}%)</p>
                    </div>
                </div>
                <div class="recipient-stat-status">
                    ${successfulMessages > 0 ? `<span class="status-badge success">${successfulMessages} نجح</span>` : ''}
                    ${failedMessages > 0 ? `<span class="status-badge failed">${failedMessages} فشل</span>` : ''}
                    ${completedMessages < totalMessages ? `<span class="status-badge pending">${totalMessages - completedMessages} معلق</span>` : ''}
                </div>
            </div>
        `;
    });

    recipientsList.innerHTML = html;

    // تطبيق الفلاتر
    applyRecipientsFilter();
}

/**
 * تطبيق فلاتر المستلمين
 */
function applyRecipientsFilter() {
    const activeFilter = document.querySelector('.filter-btn.active')?.dataset.filter || 'all';
    const recipientItems = document.querySelectorAll('.recipient-stat-item');

    recipientItems.forEach(item => {
        const status = item.dataset.status;
        const shouldShow = activeFilter === 'all' || status === activeFilter;
        item.style.display = shouldShow ? 'flex' : 'none';
    });
}

/**
 * تحديث عنصر HTML
 */
function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

/**
 * تنظيف ملفات العمليات
 */
async function cleanupOperationFiles() {
    try {
        const response = await fetch('/api/cleanup-operations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const result = await response.json();

            // إظهار رسالة نجاح
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'تم التنظيف بنجاح',
                    text: `تم حذف ${result.deletedCount || 0} ملف عملية`,
                    icon: 'success',
                    confirmButtonText: 'حسناً',
                    confirmButtonColor: '#25D366'
                });
            } else {
                alert(`تم حذف ${result.deletedCount || 0} ملف عملية بنجاح`);
            }

            console.log(`✅ تم حذف ${result.deletedCount || 0} ملف عملية`);
        } else {
            throw new Error('فشل في تنظيف ملفات العمليات');
        }
    } catch (error) {
        console.error('خطأ في تنظيف ملفات العمليات:', error);

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'خطأ',
                text: 'فشل في تنظيف ملفات العمليات',
                icon: 'error',
                confirmButtonText: 'حسناً',
                confirmButtonColor: '#d33'
            });
        } else {
            alert('فشل في تنظيف ملفات العمليات');
        }
    }
}

/**
 * تحديث الإحصائيات يدوياً
 */
function refreshStatistics() {
    const refreshBtn = document.querySelector('.refresh-btn i');
    if (refreshBtn) {
        refreshBtn.classList.add('fa-spin');
        setTimeout(() => {
            refreshBtn.classList.remove('fa-spin');
        }, 1000);
    }

    updateStatisticsDisplay();
}

// إضافة مستمعات الأحداث للفلاتر
document.addEventListener('DOMContentLoaded', function() {
    // مستمعات أحداث أزرار الفلتر
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('filter-btn')) {
            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // إضافة الفئة النشطة للزر المحدد
            e.target.classList.add('active');

            // تطبيق الفلتر
            applyRecipientsFilter();
        }
    });

    // إيقاف التحديث عند مغادرة الصفحة
    window.addEventListener('beforeunload', function() {
        stopStatisticsUpdates();
    });
});

// تم حذف الدوال المسؤولة عن شريط التقدم


// تم حذف الدوال المسؤولة عن شريط التقدم

/**
 * ترجمة حالة WhatsApp إلى نص عربي
 * @param {string|number} status - حالة الرسالة
 * @returns {string} النص العربي المقابل للحالة
 */
function getStatusText(status) {
    if (typeof status === 'number') {
        switch (status) {
            case 0: return 'قيد الإرسال';
            case 1: return 'تم الإرسال';
            case 2: return 'تم التسليم';
            case 3: return 'تم القراءة';
            default: return 'غير معروف';
        }
    }

    switch (status) {
        case 'pending': return 'قيد الإرسال';
        case 'sent': return 'تم الإرسال';
        case 'delivered': return 'تم التسليم';
        case 'received': return 'تم التسليم';
        case 'read': return 'تم القراءة';
        case 'played': return 'تم القراءة';
        case 'failed': return 'فشل الإرسال';
        case 'error': return 'فشل الإرسال';
        default: return 'غير معروف';
    }
}