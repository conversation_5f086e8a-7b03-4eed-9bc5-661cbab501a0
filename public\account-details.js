document.addEventListener('DOMContentLoaded', function() {
    // إضافة أنماط CSS لتحسين مظهر الصورة والتفاعل
    const style = document.createElement('style');
    style.textContent = `
        .contact-image {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .contact-image-placeholder {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
        }
        .contact-row {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .contact-row:hover {
            background-color: rgba(37, 211, 102, 0.05);
        }
        .contact-row.selected-row {
            background-color: rgba(37, 211, 102, 0.1);
        }
        .contact-name {
            font-weight: 500;
        }
    `;
    document.head.appendChild(style);

    // الحصول على اسم الحساب من معلمات URL
    const urlParams = new URLSearchParams(window.location.search);
    const accountName = urlParams.get('account');

    // التحقق من وجود اسم الحساب
    if (!accountName) {
        Swal.fire({
            title: 'خطأ',
            text: 'لم يتم تحديد اسم الحساب',
            icon: 'error',
            confirmButtonText: 'العودة للرئيسية',
            confirmButtonColor: '#25D366'
        }).then(() => {
            window.location.href = '/';
        });
        return;
    }

    // تعيين اسم الحساب في العنوان
    document.getElementById('accountName').textContent = accountName;

    // إضافة مستمع الحدث لزر العودة
    document.getElementById('backBtn').addEventListener('click', function() {
        window.location.href = '/';
    });

    // إضافة مستمع الحدث لزر عرض جهات الاتصال
    document.getElementById('loadContactsBtn').addEventListener('click', function() {
        // إظهار جدول جهات الاتصال وإخفاء جدول المجموعات إذا كان ظاهرًا
        document.querySelector('.contacts-table h3').textContent = 'جهات الاتصال';
        if (document.getElementById('groupContactsContainer')) {
            document.getElementById('groupContactsContainer').style.display = 'none';
        }
        document.getElementById('contactsContainer').style.display = 'block';

        // عند الضغط على زر التحميل، نحاول دائماً التحديث إلا إذا كان المستخدم قد ضغط عليه بالفعل مؤخراً
        const lastClickTime = parseInt(localStorage.getItem(`${accountName}_last_contacts_refresh`) || '0');
        const currentTime = Date.now();
        const timeDiff = currentTime - lastClickTime;

        if (timeDiff < 60000) { // أقل من دقيقة واحدة منذ آخر تحديث
            // عرض تنبيه للمستخدم
            const secondsPassed = Math.floor(timeDiff / 1000);
            Swal.fire({
                title: 'تأكيد التحديث',
                text: `لقد قمت بتحديث جهات الاتصال منذ ${secondsPassed} ثانية. هل تريد التحديث مرة أخرى؟`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، تحديث',
                cancelButtonText: 'لا، عرض البيانات الحالية',
                confirmButtonColor: '#25D366'
            }).then((result) => {
                if (result.isConfirmed) {
                    // تحديث جهات الاتصال من الخادم
                    localStorage.setItem(`${accountName}_last_contacts_refresh`, currentTime.toString());
                    loadContacts(true);
                } else {
                    // استخدام البيانات المحملة مسبقاً أو تحميلها من الملف المخزن
                    if (allContacts && allContacts.length > 0) {
                        displayContactsPage(currentPage);
                    } else {
                        loadContactsFromJson();
                    }
                }
            });
        } else {
            // تخزين وقت آخر تحديث
            localStorage.setItem(`${accountName}_last_contacts_refresh`, currentTime.toString());
            // تحديث جهات الاتصال من الخادم
            loadContacts(true);
        }
    });

    // إضافة مستمع الحدث لحاوية إحصائيات المجموعات
    document.querySelector('.stats-card:nth-child(2)').addEventListener('click', function() {
        // إظهار جدول جهات الاتصال في المجموعات وإخفاء جدول جهات الاتصال العادية
        document.querySelector('.contacts-table h3').textContent = 'جهات الاتصال في المجموعات';
        document.getElementById('contactsContainer').style.display = 'none';

        // إنشاء حاوية لجهات الاتصال في المجموعات إذا لم تكن موجودة
        if (!document.getElementById('groupContactsContainer')) {
            const container = document.createElement('div');
            container.id = 'groupContactsContainer';
            document.querySelector('.contacts-table').appendChild(container);
        }

        document.getElementById('groupContactsContainer').style.display = 'block';
        loadGroupContacts();
    });

    // إضافة مستمع الحدث لحاوية إحصائيات جهات الاتصال
    document.querySelector('.stats-card:nth-child(3)').addEventListener('click', function() {
        // إظهار جدول جهات الاتصال وإخفاء جدول المجموعات إذا كان ظاهرًا
        document.querySelector('.contacts-table h3').textContent = 'جهات الاتصال';
        if (document.getElementById('groupContactsContainer')) {
            document.getElementById('groupContactsContainer').style.display = 'none';
        }
        document.getElementById('contactsContainer').style.display = 'block';

        // التحقق إذا كان لدينا بالفعل جهات اتصال محملة
        if (allContacts && allContacts.length > 0) {
            // عرض جهات الاتصال المحملة مسبقاً بدون تحميل من جديد
            displayContactsPage(currentPage);
        } else {
            // إذا لم تكن البيانات محملة، نحاول تحميلها من الملف المخزن أولاً
            loadContactsFromJson();
        }
    });

    // تحميل معلومات الحساب
    loadAccountInfo();

    // تحميل جهات الاتصال من ملف contacts.json مباشرة عند فتح الصفحة
    loadContactsFromJson();

    // وظيفة تحميل معلومات الحساب
    function loadAccountInfo() {
        fetch(`/api/account-details/${accountName}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showAlert(data.error, 'error');
                    return;
                }

                // تحديث صورة الملف الشخصي
                if (data.profilePictureUrl) {
                    document.getElementById('profileImage').src = data.profilePictureUrl;
                }

                // تحديث حالة الاتصال
                updateConnectionStatus(data.connected, data.status);

                // تحديث معلومات الحساب
                const accountInfoContainer = document.getElementById('accountInfo');
                // تخزين معرف المستخدم (JID و LID) في خصائص data للاستخدام لاحقًا
                if (data.jid) {
                    accountInfoContainer.dataset.jid = data.jid;
                    console.log('تم تخزين معرف المستخدم (JID):', data.jid);
                }
                // تخزين معرف LID إذا كان متاحًا
                if (data.lid) { // نفترض أن الـ API يرسل معرف LID في خاصية 'lid'
                    accountInfoContainer.dataset.lid = data.lid;
                    // تخزين الجزء الرقمي من معرف LID للمقارنة
                    accountInfoContainer.dataset.numericLid = data.lid.split(':')[0];
                    console.log('تم تخزين معرف المستخدم (LID):', data.lid);
                    console.log('تم تخزين الجزء الرقمي من معرف LID:', accountInfoContainer.dataset.numericLid);
                } else if (data.jid && data.jid.includes('@lid')) {
                    // محاولة استخراجه من JID إذا لم يكن متاحًا بشكل منفصل
                    accountInfoContainer.dataset.lid = data.jid;
                    // تخزين الجزء الرقمي من معرف LID للمقارنة
                    accountInfoContainer.dataset.numericLid = data.jid.split('@')[0].split(':')[0];
                    console.log('تم تخزين معرف المستخدم (LID) من JID:', data.jid);
                    console.log('تم تخزين الجزء الرقمي من معرف LID:', accountInfoContainer.dataset.numericLid);
                }
                accountInfoContainer.innerHTML = `
                    <p><strong>الاسم:</strong> ${data.name || 'غير معروف'}</p>
                    ${data.verifiedName ? `<p><strong>الاسم الموثق:</strong> ${data.verifiedName}</p>` : ''}
                    <p><strong>رقم الهاتف:</strong> ${data.number || 'غير معروف'}</p>
                    <p><strong>نوع الحساب:</strong> ${data.accountType || 'شخصي'}</p>
                    <p><strong>معرف الحساب:</strong> <span class="text-muted">${data.jid || 'غير معروف'}</span></p>
                `;
            })
            .catch(error => {
                console.error('Error loading account info:', error);
                showAlert('حدث خطأ أثناء تحميل معلومات الحساب', 'error');
            });
    }

    // متغيرات عالمية لجهات الاتصال
    let allContacts = [];
    let currentPage = 1;
    let pageSize = 20;
    let sortDirection = 'asc';
    let lastUpdated = null;

    // وظيفة تحميل جهات الاتصال من ملف contacts.json مباشرة
    function loadContactsFromJson() {
        // عرض مؤشر التحميل البسيط
        document.getElementById('contactsContainer').innerHTML = `
            <div class="loading-container">
                <div class="loading-info">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">جاري التحميل جهات الاتصال...</span>
                    </div>
                    <h5 class="mt-3">جاري فحص وجود بيانات محفوظة...</h5>
                </div>
            </div>
        `;

        console.log('بدء فحص وجود بيانات جهات اتصال محفوظة للحساب:', accountName);

        // التحقق من وجود ملف جهات الاتصال المحفوظة
        fetch(`/api/cached-contacts/${accountName}?page=1&pageSize=10000`)
            .then(response => {
                console.log('استجابة الخادم:', response.status, response.statusText);
                if (!response.ok) {
                    if (response.status === 404) {
                        // الملف غير موجود، نقوم بتحميل البيانات من الخادم
                        console.log('ملف جهات الاتصال غير موجود، سيتم تحميل البيانات من الخادم...');
                        throw new Error('not_found');
                    } else {
                        console.error('خطأ HTTP:', response.status, response.statusText);
                        throw new Error(`فشل في فحص حالة ملف جهات الاتصال (${response.status})`);
                    }
                }
                return response.json();
            })
                        .then(data => {
                console.log(`تم استلام البيانات، عدد جهات الاتصال:`, data.contacts ? data.contacts.length : 0);

                // التحقق من صحة البيانات
                if (!data.contacts || !Array.isArray(data.contacts) || data.contacts.length === 0) {
                    console.log('لا توجد بيانات جهات اتصال محفوظة، سيتم تحميلها من الخادم...');
                    throw new Error('empty_data');
                }

                            // تخزين جهات الاتصال والمعلومات الإضافية
                            allContacts = data.contacts;
                            lastUpdated = data.lastUpdated;

                            // تحديث عدد جهات الاتصال في الإحصائيات
                            document.getElementById('contactsCount').textContent = data.contacts.length;

                console.log(`تم تحميل ${data.contacts.length} جهة اتصال محفوظة بنجاح، تاريخ التحديث: ${lastUpdated}`);

                // إضافة معلومات التاريخ
                const lastUpdateDate = new Date(lastUpdated);
                const now = new Date();
                const diffMinutes = Math.floor((now - lastUpdateDate) / (1000 * 60));
                const diffHours = Math.floor(diffMinutes / 60);
                const diffDays = Math.floor(diffHours / 24);

                let updateInfo = '';
                if (diffDays > 0) {
                    updateInfo = `منذ ${diffDays} يوم${diffDays > 1 ? '' : ''}`; // صيغة الجمع العربية
                } else if (diffHours > 0) {
                    updateInfo = `منذ ${diffHours} ساعة${diffHours > 1 ? '' : ''}`; // صيغة الجمع العربية
                } else {
                    updateInfo = `منذ ${diffMinutes} دقيقة${diffMinutes > 1 ? '' : ''}`; // صيغة الجمع العربية
                }

                            // تحميل الصور المسبق قبل عرض جهات الاتصال
                            preloadContactImages(data.contacts, () => {
                                // عرض الصفحة الأولى بعد تحميل الصور
                            currentPage = 1;

                                // تهيئة معلومات الصفحات إذا لم تكن موجودة
                                const pagination = {
                                    page: currentPage,
                                    pageSize: pageSize,
                                    totalPages: Math.ceil(data.contacts.length / pageSize),
                                    totalItems: data.contacts.length
                                };

                                // إضافة معلومات التحديث
                                displayContacts(data.contacts, pagination, false, updateInfo);
                            });
                        })
                        .catch(error => {
                console.error('خطأ:', error.message);
                if (error.message === 'not_found' || error.message === 'empty_data') {
                    // إعلام المستخدم أننا سنحاول تحميل البيانات من الخادم
                    document.getElementById('contactsContainer').innerHTML = `
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد جهات اتصال محفوظة. جاري تحميل البيانات من الخادم...
                        </div>
                    `;
                    // تأخير قصير قبل بدء التحميل من الخادم
                    setTimeout(() => loadContacts(true), 1000);
                } else {
                    // عرض خطأ عام
                    document.getElementById('contactsContainer').innerHTML = `
                        <div class="alert alert-danger text-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            حدث خطأ أثناء تحميل بيانات جهات الاتصال: ${error.message}
                            <button class="btn btn-sm btn-success ms-2" onclick="loadContacts(true)">
                                <i class="fas fa-sync-alt me-1"></i>تحميل من الخادم
                            </button>
                        </div>
                    `;
                }
            });
    }

    // متغيرات عالمية لبيانات المجموعات
    let groupsData = null;
    let groupContacts = [];

    // وظيفة تحميل جهات الاتصال في المجموعات
    function loadGroupContacts() {
        // عرض مؤشر التحميل
        document.getElementById('groupContactsContainer').innerHTML = `
            <div class="loading-container">
                <div class="loading-info">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">جاري تحميل جهات الاتصال في المجموعات...</span>
                    </div>
                    <h5 class="mt-3">جاري تحميل بيانات المجموعات...</h5>
                </div>
            </div>
        `;

        // استخدام API لاستخراج بيانات المجموعات
        fetch(`/api/extract-groups-data/${accountName}`)
            .then(response => {
                if (!response.ok) {
                    // إذا كان الخطأ 404 (ملف بيانات المجموعات غير موجود)، نحاول تحميل البيانات أولاً
                    if (response.status === 404) {
                        console.log('ملف بيانات المجموعات غير موجود، جاري محاولة تحميل البيانات...');
                        return fetch(`/api/debug-groups/${accountName}`)
                            .then(debugResponse => {
                                if (debugResponse.ok) {
                                    return debugResponse.json();
                                } else {
                                    throw new Error('فشل في تحميل بيانات المجموعات');
                                }
                            })
                            .then(debugData => {
                                console.log('تم تحميل بيانات المجموعات بنجاح، جاري استخراج البيانات مرة أخرى...');
                                // بعد تحميل البيانات، نحاول مرة أخرى استخراج البيانات
                                return fetch(`/api/extract-groups-data/${accountName}`).then(r => r.json());
                            });
                    } else {
                        throw new Error('فشل في استخراج بيانات المجموعات');
                    }
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    document.getElementById('groupContactsContainer').innerHTML = '<p class="text-center">حدث خطأ أثناء تحميل بيانات المجموعات.</p>';
                    return;
                }

                console.log('تم استلام بيانات المجموعات:', data);

                // تخزين بيانات المجموعات - التعامل مع هيكل البيانات المختلف
                if (data.groupsData) {
                    groupsData = data.groupsData;
                } else if (data.groups) {
                    // تحويل المصفوفة إلى كائن بمعرفات المجموعات كمفاتيح
                    if (Array.isArray(data.groups)) {
                        groupsData = {};
                        data.groups.forEach(group => {
                            if (group.id) {
                                groupsData[group.id] = group;
                            }
                        });
                        console.log('🔄 تم تحويل مصفوفة المجموعات إلى كائن:', groupsData);
                    } else {
                        groupsData = data.groups;
                    }
                } else if (data.filePath) {
                    // إذا كان هناك مسار ملف، نحاول تحميل الملف مباشرة
                    fetch(`/api/get-groups-json/${accountName}`)
                        .then(response => response.json())
                        .then(jsonData => {
                            // التعامل مع البيانات المحملة من الملف
                            if (jsonData.groups && Array.isArray(jsonData.groups)) {
                                groupsData = {};
                                jsonData.groups.forEach(group => {
                                    if (group.id) {
                                        groupsData[group.id] = group;
                                    }
                                });
                                console.log('🔄 تم تحويل مصفوفة المجموعات من الملف إلى كائن:', groupsData);
                            } else {
                                groupsData = jsonData;
                            }
                            extractContactsFromGroups();
                        })
                        .catch(error => {
                            console.error('Error loading groups JSON file:', error);
                            document.getElementById('groupContactsContainer').innerHTML = '<p class="text-center">حدث خطأ أثناء تحميل ملف بيانات المجموعات.</p>';
                        });
                    return;
                } else {
                    // محاولة استخدام البيانات مباشرة إذا كانت تحتوي على مفاتيح المجموعات
                    const hasGroupKeys = Object.keys(data).some(key => key.includes('@g.us'));
                    if (hasGroupKeys) {
                        groupsData = data;
                    } else {
                        // التحقق من وجود مصفوفة groups في البيانات
                        if (data.groups && Array.isArray(data.groups)) {
                            groupsData = {};
                            data.groups.forEach(group => {
                                if (group.id) {
                                    groupsData[group.id] = group;
                                }
                            });
                            console.log('🔄 تم تحويل مصفوفة المجموعات (حالة أخيرة) إلى كائن:', groupsData);
                        } else {
                            document.getElementById('groupContactsContainer').innerHTML = '<p class="text-center">لم يتم العثور على بيانات المجموعات.</p>';
                            return;
                        }
                    }
                }

                // استخراج جهات الاتصال من المجموعات
                extractContactsFromGroups();
            })
            .catch(error => {
                console.error('Error loading groups data:', error);
                document.getElementById('groupContactsContainer').innerHTML = '<p class="text-center">حدث خطأ أثناء تحميل بيانات المجموعات.</p>';
            });
    }

    // وظيفة استخراج جهات الاتصال من بيانات المجموعات
    function extractContactsFromGroups() {
        if (!groupsData) {
            document.getElementById('groupContactsContainer').innerHTML = '<p class="text-center">لا توجد بيانات للمجموعات.</p>';
            return;
        }

        // إنشاء مصفوفة للمجموعات
        const groups = [];
        let adminGroupsCount = 0; // متغير لحساب المجموعات التي يديرها المستخدم

        // استخراج معلومات المجموعات
        for (const groupId in groupsData) {
            const group = groupsData[groupId];
            const groupName = group.name || group.subject || 'مجموعة بدون اسم';

            console.log(`🔍 معالجة مجموعة:`, {
                groupId: groupId,
                groupName: groupName,
                groupData: group
            });

            // قراءة حالة المستخدم كمدير مباشرة من البيانات المحملة
            // استخدام المفتاح isCurrentUserAdmin من الخادم
            const isUserAdmin = group.isCurrentUserAdmin === true;

            // تخزين القيمة في متغير محلي للاستخدام في واجهة المستخدم
            group.isUserAdmin = isUserAdmin;

            if (isUserAdmin) {
                adminGroupsCount++; // زيادة العداد إذا كان المستخدم مديرًا
            }

            let adminCount = 0;
            let membersCount = 0;

            // حساب عدد الأعضاء والمشرفين (لا يزال مفيدًا للعرض)
            if (group.participants && Array.isArray(group.participants)) {
                membersCount = group.participants.length;
                group.participants.forEach(participant => {
                    if (participant.admin === 'admin' || participant.admin === 'superadmin') {
                        adminCount++;
                    }
                });
            }

            // استخراج معلومات المجموعة وإضافتها إلى مصفوفة المجموعات
            const groupObject = {
                id: groupId,
                name: groupName,
                isUserAdmin: isUserAdmin, // استخدام القيمة المحملة
                membersCount: membersCount,
                adminCount: adminCount,
                creation: group.creation,
                owner: group.owner,
                desc: group.desc || '',
                size: group.size || membersCount
            };

            console.log(`✅ إضافة مجموعة للقائمة:`, groupObject);
            groups.push(groupObject);
        }

        // تحديث إحصائيات المجموعات في الواجهة (خارج الحلقة)
        const totalGroupsCount = groups.length;
        const memberGroupsCount = totalGroupsCount - adminGroupsCount;
        document.getElementById('groupsCount').textContent = totalGroupsCount;
        document.getElementById('adminGroupsCount').textContent = adminGroupsCount;
        document.getElementById('memberGroupsCount').textContent = memberGroupsCount;

        console.log('تم استخراج', groups.length, 'مجموعة');

        // عرض المجموعات في جدول
        displayGroupsTable(groups);
    }

    // وظيفة لعرض قائمة منسدلة للمجموعات
    function displayGroupsTable(groups) {
        const container = document.getElementById('groupContactsContainer');

        if (!groups || groups.length === 0) {
            container.innerHTML = '<p class="text-center">لا توجد مجموعات متاحة.</p>';
            return;
        }

        // إضافة CSS لتنسيق الصفوف التي تمثل المجموعات التي يكون المستخدم فيها مديرًا
        // التحقق من عدم وجود نفس العنصر مسبقًا
        if (!document.getElementById('admin-row-style')) {
            const style = document.createElement('style');
            style.id = 'admin-row-style';
            style.textContent = `
                .admin-row {
                    background-color: rgba(25, 135, 84, 0.1) !important;
                    font-weight: bold;
                }
                .admin-row:hover {
                    background-color: rgba(25, 135, 84, 0.2) !important;
                }
            `;
            document.head.appendChild(style);
        }

        // إنشاء جدول المجموعات
        let tableHTML = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <button id="refreshGroupsBtn" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-sync-alt me-1"></i>تحديث
                    </button>
                </div>
                <div class="d-flex align-items-center">
                    <input type="text" id="groupSearchInput" class="form-control form-control-sm" placeholder="البحث في المجموعات...">
                </div>
            </div>

            <!-- زر اختيار الكل -->
            <div class="mb-3">
                <button id="selectAllGroupsBtn" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-check-square me-1"></i>اختيار الكل
                </button>
                <small class="text-muted me-2">إضافة جميع المجموعات المعروضة حالياً (${groups.length}) إلى العناصر المختارة</small>
            </div>
            <div class="table-responsive">
                <table class="table table-hover contacts-table">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>اسم المجموعة</th>
                            <th>عدد الأعضاء</th>
                            <th>عدد المشرفين</th>
                            <th>دورك في المجموعة</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        // إضافة صفوف المجموعات
        groups.forEach(group => {
            // تحديد أيقونة المدير إذا كان المستخدم مديرًا في المجموعة
            const adminBadge = group.isUserAdmin ? '<i class="fas fa-crown text-warning me-1" title="أنت مدير"></i>' : '';

            // تحديد لون خلفية حالة المجموعة بناءً على ما إذا كان المستخدم مديرًا في المجموعة
            const statusClass = group.isUserAdmin ? 'bg-success text-white' : 'bg-secondary text-white';

            // تحسين عرض أيقونة الحالة
            const statusIcon = group.isUserAdmin ?
                '<i class="fas fa-user-shield me-1" title="أنت مدير في هذه المجموعة"></i>' :
                '<i class="fas fa-user me-1" title="أنت عضو في هذه المجموعة"></i>';

            // إضافة شارة إضافية إذا كان المستخدم هو مدير المجموعة
            const adminIndicator = group.isUserAdmin ? '<span class="badge bg-warning text-dark ms-2"><i class="fas fa-star me-1"></i>أنت المدير</span>' : '';

            // استخدام صورة افتراضية للمجموعة
            const groupImageUrl = 'https://cdn.pixabay.com/photo/2017/11/10/05/46/group-2935521_1280.png';

            // تحديد لون خلفية الصف بناءً على ما إذا كان المستخدم مديرًا في المجموعة
            const rowClass = group.isUserAdmin ? 'admin-row' : '';

            tableHTML += `
                <tr data-group-id="${group.id}" data-group-name="${group.name}" class="${rowClass} group-row selectable-row">
                    <td>
                        <img src="${groupImageUrl}"
                             alt="صورة المجموعة"
                             class="contact-image"
                             data-bs-toggle="modal"
                             data-bs-target="#imageModal"
                             data-image-url="${groupImageUrl}"
                             data-contact-name="${group.name}"
                             style="cursor: pointer;"
                             onerror="this.src='https://cdn.pixabay.com/photo/2017/11/10/05/46/group-2935521_1280.png'">
                    </td>
                    <td>
                        <div class="contact-name">
                            ${adminBadge} ${group.name}
                        </div>
                    </td>
                    <td>${group.membersCount}</td>
                    <td>${group.adminCount}</td>
                    <td>
                        <span class="badge ${statusClass}">
                            ${statusIcon} ${group.isUserAdmin ? 'مدير' : 'عضو'}
                        </span>
                        ${adminIndicator}
                    </td>
                </tr>
            `;

            // تم إضافة CSS في بداية الدالة
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        // عرض الجدول
        container.innerHTML = tableHTML;

        // إضافة مستمعي الأحداث للصفوف مباشرة
        document.querySelectorAll('#groupContactsContainer .group-row').forEach(row => {
            if (!row.hasAttribute('data-has-click-listener')) {
                row.setAttribute('data-has-click-listener', 'true');

                row.addEventListener('click', function() {
                    const groupId = this.getAttribute('data-group-id');
                    const groupName = this.getAttribute('data-group-name');

                    console.log(`🔍 نقر على مجموعة:`, {
                        groupId: groupId,
                        groupName: groupName,
                        rowElement: this
                    });

                    // التحقق من عدم وجود العنصر مسبقًا
                    const exists = window.selectedItems.some(item =>
                        item.type === 'group' && item.id === groupId
                    );

                    if (!exists) {
                        const groupItem = {
                            id: groupId,
                            name: groupName,
                            image: '',
                            type: 'group'
                        };

                        console.log(`✅ إضافة مجموعة لـ selectedItems:`, groupItem);
                        window.selectedItems.push(groupItem);

                        window.updateSelectedItemsTable();

                        // إظهار إشعار
                        Swal.fire({
                            title: 'تمت الإضافة',
                            text: `تمت إضافة مجموعة ${groupName} إلى القائمة المختارة`,
                            icon: 'success',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000
                        });
                    }
                });
            }
        });

        // استدعاء دالة إضافة مستمعي الأحداث للصفوف
        if (typeof addClickListenersToRows === 'function') {
            addClickListenersToRows();
        }

        // إضافة مستمع الحدث لزر التحديث
        const refreshBtn = document.getElementById('refreshGroupsBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => loadGroupContacts());
        }

        // إضافة مستمع الحدث للبحث في المجموعات
        const searchInput = document.getElementById('groupSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const rows = document.querySelectorAll('#groupContactsContainer tbody tr');

                rows.forEach(row => {
                    const groupName = row.querySelector('.contact-name').textContent.toLowerCase();
                    if (groupName.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }

        // إضافة مستمعات الأحداث للصور
        addImageModalEventListeners();
    }

    // وظيفة عرض جهات الاتصال في المجموعات
    function displayGroupContacts(contacts, selectedGroupId = 'all') {
        const container = document.getElementById('groupMembersTable');

        if (!contacts || contacts.length === 0) {
            container.innerHTML = '<p class="text-center">لا توجد جهات اتصال في المجموعات.</p>';
            return;
        }

        // تصفية جهات الاتصال حسب المجموعة المحددة
        let filteredContacts = contacts;
        if (selectedGroupId !== 'all') {
            filteredContacts = contacts.filter(contact => contact.groupId === selectedGroupId);
        }

        if (filteredContacts.length === 0) {
            container.innerHTML = '<p class="text-center">لا توجد جهات اتصال في المجموعة المحددة.</p>';
            return;
        }

        // إنشاء جدول جهات الاتصال في المجموعات
        let tableHTML = `
            <div class="table-responsive">
                <table class="table table-hover contacts-table">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>الاسم</th>
                            <th>رقم الهاتف</th>
                            <th>اسم المجموعة</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        // إضافة صفوف جهات الاتصال
        filteredContacts.forEach(contact => {
            // التحقق مما إذا كان الرقم يبدأ بـ lid: لمعالجة معرفات فيسبوك بشكل مختلف
            const isLidContact = contact.number.startsWith('lid:');

            // البحث عن معلومات جهة الاتصال في قائمة جهات الاتصال الرئيسية
            const contactInfo = !isLidContact && allContacts ? allContacts.find(c => c.number === contact.number) : null;

            // استخدام معلومات جهة الاتصال إذا وجدت، وإلا استخدام رقم الهاتف فقط
            let contactName;
            if (isLidContact) {
                contactName = `معرف فيسبوك: ${contact.number.replace('lid:', '')}`;
            } else {
                contactName = contactInfo ? (contactInfo.pushName || contactInfo.savedName || contactInfo.name || contact.number) : contact.number;
            }

            const profilePictureUrl = contactInfo ? contactInfo.profilePictureUrl : null;

            // تحديد لون خلفية اسم المجموعة بناءً على ما إذا كان المستخدم مديرًا في المجموعة
            const groupNameClass = contact.isUserAdmin ? 'bg-success text-white' : 'bg-secondary text-white';

            // تحديد أيقونة المدير إذا كان المشارك مديرًا في المجموعة
            const adminIcon = contact.isGroupAdmin ? '<i class="fas fa-crown text-warning me-1" title="مدير المجموعة"></i>' : '';

            tableHTML += `
                <tr>
                    <td>
                        <img src="${profilePictureUrl || 'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png'}"
                             alt="صورة جهة الاتصال"
                             class="contact-image"
                             data-bs-toggle="modal"
                             data-bs-target="#imageModal"
                             data-image-url="${profilePictureUrl || 'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png'}"
                             data-contact-name="${contactName}"
                             style="cursor: pointer;"
                             onerror="this.src='https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png'">
                    </td>
                    <td>
                        <div class="contact-name">
                            ${adminIcon} ${contactName}
                        </div>
                    </td>
                    <td>${isLidContact ? 'غير متاح' : contact.number}</td>
                    <td>
                        <span class="badge ${groupNameClass}">
                            ${contact.groupName}
                        </span>
                    </td>
                    <td>
                        ${contact.isGroupAdmin ?
                            '<span class="badge bg-warning text-dark">مدير</span>' :
                            '<span class="badge bg-info text-dark">عضو</span>'}
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        // عرض الجدول
        container.innerHTML = tableHTML;

        // إضافة مستمعات الأحداث للصور
        addImageModalEventListeners();
    }

    // وظيفة تحميل جهات الاتصال
    function loadContacts(refresh = false) {
        const contactsContainer = document.getElementById('contactsContainer'); // تعريف المتغير هنا
        // عرض مؤشر التحميل مع شريط التقدم
        document.getElementById('contactsContainer').innerHTML = `
            <div class="loading-container">
                <div class="loading-info">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">جاري تحميل جهات الاتصال...</span>
                    </div>
                    <h5 class="mt-3">${refresh ? 'جاري تحميل جهات الاتصال من الخادم...' : 'جاري تحميل جهات الاتصال المحفوظة...'}</h5>
                    <div class="progress mt-2" style="height: 15px; width: 100%;">
                        <div id="loadingProgress" class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                             role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <p id="loadingStatus" class="text-muted mt-2">جاري التحضير...</p>
                </div>
            </div>
        `;

        // تحديد عنوان API المناسب بناءً على حالة التحديث
        const apiUrl = refresh ?
            `/api/contacts/${accountName}?refresh=true` :
            `/api/cached-contacts/${accountName}?page=${currentPage}&pageSize=${pageSize}`;

        // الحصول على عناصر التقدم
        const progressBar = document.getElementById('loadingProgress');
        const loadingStatus = document.getElementById('loadingStatus');

        // إذا كان هذا تحميلًا من الذاكرة المخزنة (لا يوجد تحديث)
        if (!refresh) {
            progressBar.style.width = '50%';
            progressBar.setAttribute('aria-valuenow', 50);
            progressBar.textContent = '50%';
            loadingStatus.textContent = 'جاري تحميل البيانات المحفوظة...';

            // استخدام fetch العادي للبيانات المخزنة
            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        // إذا كان الملف غير موجود، نقوم بالتحديث تلقائياً
                        if (response.status === 404) {
                            loadingStatus.textContent = 'ملف جهات الاتصال غير موجود، جاري إنشاء ملف جديد وتحميل البيانات من الخادم...';

                            // تحديث شريط التقدم لإظهار أننا بدأنا عملية جديدة
                            progressBar.style.width = '10%';
                            progressBar.setAttribute('aria-valuenow', 10);
                            progressBar.textContent = '10%';

                            setTimeout(() => loadContacts(true), 1000); // تحميل البيانات من الخادم بعد إظهار الرسالة
                            return null;
                        }
                        throw new Error(`فشل في تحميل البيانات المخزنة (${response.status})`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (!data) return; // الاستجابة تم معالجتها بالفعل (مثل 404)

                    // إكمال شريط التقدم
                    progressBar.style.width = '70%';
                    progressBar.setAttribute('aria-valuenow', 70);
                    progressBar.textContent = '70%';
                    loadingStatus.textContent = 'جاري تحميل صور جهات الاتصال...';

                    if (data.error) {
                        showAlert(data.error, 'error');
                        contactsContainer.innerHTML = `<p class="text-center text-danger">${data.error}. <button class="btn btn-sm btn-primary" onclick="loadContacts(false)">حاول مرة أخرى</button></p>`;
                        return;
                    }

                    const contactsContainer = document.getElementById('contactsContainer');

                    if (!data.contacts || data.contacts.length === 0) {
                        contactsContainer.innerHTML = `
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد جهات اتصال محفوظة.
                                <button class="btn btn-sm btn-success ms-2" id="refreshContactsNowBtn">
                                    <i class="fas fa-sync-alt me-1"></i>تحميل جهات الاتصال الآن
                                </button>
                            </div>
                        `;

                        // إضافة حدث لزر التحديث الفوري
                        document.getElementById('refreshContactsNowBtn').addEventListener('click', () => {
                            loadContacts(true);
                        });

                        return;
                    }

                    // تخزين جهات الاتصال والمعلومات الإضافية
                    allContacts = data.contacts;
                    lastUpdated = data.lastUpdated;

                    // تحديث عدد جهات الاتصال في الإحصائيات
                    document.getElementById('contactsCount').textContent = data.contacts.length;

                    // تحميل الصور مسبقًا لتحسين تجربة المستخدم
                    preloadContactImages(data.contacts, () => {
                        // إكمال شريط التقدم بعد تحميل الصور
                        progressBar.style.width = '100%';
                        progressBar.setAttribute('aria-valuenow', 100);
                        progressBar.textContent = '100%';
                        loadingStatus.textContent = 'اكتمل التحميل!';

                        // عرض جهات الاتصال بعد تأخير قصير لإظهار اكتمال التحميل
                        setTimeout(() => {
                            // عرض جهات الاتصال
                            if (data.pagination) {
                                displayContacts(data.contacts, data.pagination);
                            } else {
                                // إذا لم يكن هناك معلومات صفحات، نقوم بإنشائها
                                const pagination = {
                                    page: currentPage,
                                    pageSize: pageSize,
                                    totalPages: Math.ceil(data.contacts.length / pageSize),
                                    totalItems: data.contacts.length
                                };
                                displayContacts(data.contacts, pagination);
                            }
                        }, 300);
                    });
                })
                .catch(error => {
                    console.error('خطأ في تحميل جهات الاتصال المخزنة:', error);
                    showAlert('حدث خطأ أثناء تحميل جهات الاتصال المخزنة، سيتم محاولة التحميل من الخادم', 'error');
                    contactsContainer.innerHTML = `<p class="text-center text-danger">حدث خطأ: ${error.message}. <button class="btn btn-sm btn-primary" onclick="loadContacts(false)">حاول مرة أخرى</button></p>`;
                    // في حالة حدوث خطأ، نحاول التحميل من الخادم
                    setTimeout(() => loadContacts(true), 1500);
                });
        } else {
            // استخدام Server-Sent Events للحصول على تحديثات التقدم الحقيقية عند التحديث من الخادم
            const eventSource = new EventSource(apiUrl);
            let eventSourceTimeout;

            const startTimeout = () => {
                clearTimeout(eventSourceTimeout);
                eventSourceTimeout = setTimeout(() => {
                    eventSource.close();
                    console.error('EventSource timeout after 120 seconds of inactivity.');
                    showAlert('انتهت مهلة تحميل جهات الاتصال من الخادم بسبب عدم النشاط. يرجى المحاولة مرة أخرى.', 'error');
                    contactsContainer.innerHTML = `
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            انتهت مهلة الاتصال بالخادم. قد يكون الاتصال بطيئًا أو توجد مشكلة.
                            <button class="btn btn-sm btn-primary ms-2" onclick="loadContacts(true)">
                                <i class="fas fa-sync-alt me-1"></i>إعادة محاولة التحميل من الخادم
                            </button>
                            <button class="btn btn-sm btn-outline-secondary ms-2" onclick="loadContacts(false)">
                                <i class="fas fa-database me-1"></i>محاولة تحميل البيانات المحفوظة
                            </button>
                        </div>
                    `;
                }, 120000); // 120 ثانية مهلة
            };

            startTimeout(); // بدء المؤقت عند إنشاء EventSource

            // إضافة مستمع للأحداث العادية (تحديثات التقدم)
            eventSource.onmessage = function(event) {
                startTimeout(); // إعادة تعيين المؤقت مع كل رسالة
                try {
                    const progressData = JSON.parse(event.data);

                    // تحديث شريط التقدم
                    const progressPercent = progressData.progress.toFixed(1);
                    progressBar.style.width = `${progressPercent}%`;
                    progressBar.setAttribute('aria-valuenow', progressPercent);
                    progressBar.textContent = `${progressPercent}%`;

                    // تحديث حالة التحميل مع المزيد من المعلومات التفصيلية
                    let statusText = '';
                    let detailText = '';

                    if (progressData.loaded && progressData.total) {
                        detailText = ` (${progressData.loaded}/${progressData.total})`;
                    }

                    switch (progressData.step) {
                        case 'initialization':
                            statusText = `جاري التحضير...`;
                            break;
                        case 'fetching_contacts':
                            statusText = `جاري استرجاع جهات الاتصال${detailText}`;
                            // إضافة معلومات إضافية في سجل وحدة التحكم
                            if (progressData.loaded % 20 === 0 || progressData.loaded === progressData.total) {
                                console.log(`تم استرجاع ${progressData.loaded} من ${progressData.total} جهة اتصال (${progressPercent}%)`);
                            }
                            break;
                        case 'fetching_chats':
                            statusText = `جاري استرجاع المحادثات${detailText}`;
                            break;
                        case 'adding_self':
                            statusText = `جاري إضافة الحساب الحالي${detailText}`;
                            break;
                        case 'loading_profile_pictures':
                            statusText = `جاري تحميل صور الملفات الشخصية${detailText}`;
                            break;
                        case 'sorting_contacts':
                            statusText = `جاري ترتيب وتنظيم ${progressData.total} جهة اتصال`;
                            break;
                        case 'saving_contacts':
                            statusText = `جاري حفظ ${progressData.total} جهة اتصال في ملف JSON...`;
                            // تتبع في سجل وحدة التحكم
                            console.log(`بدء حفظ ${progressData.total} جهة اتصال...`);
                            break;
                        case 'completed':
                            statusText = `اكتمل التحميل والحفظ! (${progressData.total} جهة اتصال)`;
                            console.log(`اكتمل تحميل وحفظ ${progressData.total} جهة اتصال`);
                            break;
                        default:
                            statusText = `جاري التحميل${detailText}...`;
                    }

                    // عرض رسائل مساعدة لعمليات التحميل الطويلة
                    if (progressData.total > 300 && progressData.loaded > 50 && progressData.loaded < progressData.total - 50) {
                        // إضافة نص إضافي للمستخدم في حالة استغراق وقت طويل
                        statusText += " <small class='text-muted'>(يمكن أن تستغرق العملية وقتاً للأعداد الكبيرة)</small>";
                    }

                    loadingStatus.innerHTML = statusText; // استخدام innerHTML لدعم HTML في الرسائل
                } catch (error) {
                    console.error('خطأ في تحليل بيانات التقدم:', error);
                    loadingStatus.textContent = 'جاري التحميل... (لا يمكن تحديث التقدم)';
                }
            };

            // إضافة مستمع لحدث contactsData
            eventSource.addEventListener('contactsData', function(event) {
                clearTimeout(eventSourceTimeout); // إلغاء المؤقت عند استلام البيانات النهائية
                try {
                    const data = JSON.parse(event.data);

                    // إغلاق EventSource بعد استلام البيانات
                    eventSource.close();

                    // إكمال شريط التقدم
                    progressBar.style.width = '100%';
                    progressBar.setAttribute('aria-valuenow', 100);
                    progressBar.textContent = '100%';
                    loadingStatus.textContent = 'اكتمل التحميل بنجاح!';

                    // تأخير قصير لإظهار اكتمال التحميل
                    setTimeout(() => {
                        if (data.error) {
                            showAlert(data.error, 'error');
                            contactsContainer.innerHTML = `<p class="text-center text-danger">${data.error}. <button class="btn btn-sm btn-primary" onclick="loadContacts(true)">حاول مرة أخرى</button></p>`;
                            return;
                        }

                        const contactsContainer = document.getElementById('contactsContainer');

                        if (!data.contacts || data.contacts.length === 0) {
                            contactsContainer.innerHTML = '<p class="text-center">لا توجد جهات اتصال</p>';
                            return;
                        }

                        // تسجيل معلومات تشخيصية إضافية
                        console.log(`تم استلام ${data.contacts.length} جهة اتصال من الخادم`);
                        if (data.success) {
                            console.log('تم حفظ البيانات بنجاح على الخادم');
                        } else if (data.recovered) {
                            console.log('تم استرداد البيانات باستخدام آلية التعافي');
                        } else if (data.directSave) {
                            console.log('تم حفظ البيانات باستخدام الكتابة المباشرة');
                        } else if (data.emergency) {
                            console.log('تم حفظ البيانات باستخدام آلية الطوارئ');
                        }

                        // تخزين جهات الاتصال والمعلومات الإضافية
                        allContacts = data.contacts;
                        lastUpdated = data.lastUpdated;

                        // تحديث عدد جهات الاتصال في الإحصائيات
                        document.getElementById('contactsCount').textContent = data.contacts.length;

                        // عرض الصفحة الأولى
                        currentPage = 1;
                        displayContactsPage(currentPage);

                        // إظهار رسالة نجاح مؤقتة
                        Swal.fire({
                            title: 'تم التحديث',
                            text: `تم تحميل ${data.contacts.length} جهة اتصال وحفظها بنجاح`,
                            icon: 'success',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000
                        });
                    }, 500);
                } catch (error) {
                    console.error('خطأ في تحليل بيانات جهات الاتصال:', error);
                    showAlert('حدث خطأ أثناء معالجة بيانات جهات الاتصال', 'error');
                    contactsContainer.innerHTML = `<p class="text-center text-danger">حدث خطأ أثناء معالجة البيانات: ${error.message}. <button class="btn btn-sm btn-primary" onclick="loadContacts(true)">حاول مرة أخرى</button></p>`;
                }
            });

            // إضافة مستمع خاص لأحداث الخطأ من الخادم
            eventSource.addEventListener('error', function(event) {
                clearTimeout(eventSourceTimeout);
                try {
                    const errorData = JSON.parse(event.data);
                    console.error('خطأ من الخادم:', errorData);

                    // إغلاق EventSource
                    eventSource.close();

                    // عرض رسالة الخطأ للمستخدم
                    showAlert(errorData.message || 'حدث خطأ أثناء معالجة جهات الاتصال على الخادم', 'error');

                    // عرض خيارات للمستخدم
                    contactsContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>خطأ من الخادم</h5>
                            <p>${errorData.message || 'حدث خطأ أثناء معالجة جهات الاتصال'}</p>
                            <div class="mt-3">
                                <button class="btn btn-sm btn-primary" onclick="loadContacts(true)">
                                    <i class="fas fa-sync-alt me-1"></i>إعادة المحاولة
                                </button>
                                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="loadContacts(false)">
                                    <i class="fas fa-database me-1"></i>تحميل البيانات المخزنة
                                </button>
                            </div>
                        </div>
                    `;
                } catch (parseError) {
                    console.error('خطأ في تحليل بيانات الخطأ:', parseError);

                    // إغلاق EventSource
                    eventSource.close();

                    // عرض رسالة خطأ عامة
                    showAlert('حدث خطأ غير متوقع أثناء الاتصال بالخادم', 'error');

                    // عرض خيارات للمستخدم
                    contactsContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>خطأ في الاتصال</h5>
                            <p>حدث خطأ غير متوقع أثناء جلب جهات الاتصال</p>
                            <div class="mt-3">
                                <button class="btn btn-sm btn-primary" onclick="loadContacts(true)">
                                    <i class="fas fa-sync-alt me-1"></i>إعادة المحاولة
                                </button>
                                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="loadContacts(false)">
                                    <i class="fas fa-database me-1"></i>تحميل البيانات المخزنة
                                </button>
                            </div>
                        </div>
                    `;
                }
            });

            // معالجة أخطاء EventSource
            eventSource.onerror = function(error) {
                clearTimeout(eventSourceTimeout); // إلغاء المؤقت عند حدوث خطأ
                console.error('خطأ في EventSource:', error);
                eventSource.close();

                // عرض رسالة خطأ
                showAlert('حدث خطأ أثناء تحميل جهات الاتصال من الخادم. قد يكون الاتصال انقطع أو الخادم غير متاح.', 'error');

                // محاولة استخدام البيانات المخزنة كخطة بديلة
                contactsContainer.innerHTML = `
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        حدث خطأ أثناء تحميل جهات الاتصال من الخادم.
                        <button class="btn btn-sm btn-primary ms-2" onclick="loadContacts(true)">
                            <i class="fas fa-sync-alt me-1"></i>إعادة محاولة التحميل من الخادم
                        </button>
                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="loadContacts(false)">
                            <i class="fas fa-database me-1"></i>محاولة تحميل البيانات المخزنة
                        </button>
                    </div>
                `;
            };
        }
    }

    // متغير لتخزين نص البحث الحالي
    let currentSearchQuery = '';

    // وظيفة التحميل المسبق لصور جهات الاتصال
    function preloadContactImages(contacts, callback) {
        // إذا لم تكن هناك جهات اتصال أو كان المصفوفة فارغة
        if (!contacts || contacts.length === 0) {
            if (typeof callback === 'function') callback();
            return;
        }

        console.log(`بدء تحميل صور ${contacts.length} جهة اتصال...`);

        // عدد الصور التي تم تحميلها بنجاح
        let loadedCount = 0;
        // عدد الصور التي فشل تحميلها
        let failedCount = 0;
        // إجمالي عدد الصور التي يجب تحميلها
        const totalImages = contacts.filter(contact => contact.profilePictureUrl).length;

        // إذا لم تكن هناك صور للتحميل
        if (totalImages === 0) {
            if (typeof callback === 'function') callback();
            return;
        }

        console.log(`وجد ${totalImages} صورة للتحميل...`);

        // تحميل كل صورة على حدة
        contacts.forEach(contact => {
            if (contact.profilePictureUrl) {
                const img = new Image();

                img.onload = function() {
                    loadedCount++;
                    // استدعاء الدالة عند اكتمال تحميل جميع الصور
                    if (loadedCount + failedCount === totalImages) {
                        console.log(`تم تحميل ${loadedCount} صورة بنجاح، وفشل في تحميل ${failedCount} صورة.`);
                        if (typeof callback === 'function') callback();
                    }
                };

                img.onerror = function() {
                    failedCount++;
                    // استدعاء الدالة عند اكتمال تحميل جميع الصور
                    if (loadedCount + failedCount === totalImages) {
                        console.log(`تم تحميل ${loadedCount} صورة بنجاح، وفشل في تحميل ${failedCount} صورة.`);
                        if (typeof callback === 'function') callback();
                    }
                };

                img.src = contact.profilePictureUrl;
            }
        });

        // تعيين مهلة زمنية للتأكد من استدعاء الدالة حتى لو فشل تحميل بعض الصور
        setTimeout(() => {
            if (loadedCount + failedCount < totalImages) {
                console.log(`انتهت مهلة تحميل الصور. تم تحميل ${loadedCount} صورة بنجاح، وفشل في تحميل ${failedCount} صورة.`);
                if (typeof callback === 'function') callback();
            }
        }, 5000); // مهلة 5 ثوان
    }

    // دالة البحث في جهات الاتصال
    function searchContacts(query) {
        // التأكد من وجود جهات اتصال
        if (!allContacts || allContacts.length === 0) {
            return;
        }

        // تحويل البحث إلى حروف صغيرة وتنظيفه
        query = query.toLowerCase().trim();

        if (query.length === 0) {
            // إذا كان البحث فارغًا، عرض كل جهات الاتصال
            return displayContactsPage(currentPage);
        }

        console.log(`بحث عن: "${query}" في ${allContacts.length} جهة اتصال`);

        // البحث في جهات الاتصال
        const filteredContacts = allContacts.filter(contact => {
            // البحث في الاسم أو رقم الهاتف
            const name = (contact.name || '').toLowerCase();
            const pushName = (contact.pushName || '').toLowerCase();
            const savedName = (contact.savedName || '').toLowerCase();
            const verifiedName = (contact.verifiedName || '').toLowerCase();
            const number = (contact.number || '').toLowerCase();

            // ابحث في كل الحقول
            return name.includes(query) ||
                   pushName.includes(query) ||
                   savedName.includes(query) ||
                   verifiedName.includes(query) ||
                   number.includes(query);
        });

        console.log(`تم العثور على ${filteredContacts.length} نتيجة`);

        // إعادة تعيين أيقونة البحث
        resetSearchIcon();

        // إنشاء معلومات الصفحات
        const pagination = {
            page: 1,
            pageSize: filteredContacts.length, // عرض كل النتائج في صفحة واحدة
            totalPages: 1,
            totalItems: filteredContacts.length
        };

        // عرض نتائج البحث
        displayContacts(filteredContacts, pagination, true);
    }

    // دالة إعادة تعيين أيقونة البحث
    function resetSearchIcon() {
        const searchIcon = document.getElementById('search-icon');
            if (searchIcon) {
            searchIcon.innerHTML = '<i class="fas fa-search"></i>';
        }
    }

    // وظيفة تأخير البحث لتحسين الأداء
    function debounce(func, delay) {
        let timer;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timer);
            timer = setTimeout(() => {
                func.apply(context, args);
            }, delay);
        };
    }

    // دالة لعرض صفحة محددة من جهات الاتصال
    function displayContactsPage(page) {
        // التأكد من وجود جهات اتصال
        if (!allContacts || allContacts.length === 0) {
            // إذا لم تكن جهات الاتصال محملة، نحاول تحميلها
            loadContactsFromJson();
            return;
        }

        // حساب الفهارس للصفحة المطلوبة
        const startIndex = (page - 1) * pageSize;
        const endIndex = Math.min(startIndex + pageSize, allContacts.length);

        // إنشاء معلومات الصفحات
        const pagination = {
            page: page,
            pageSize: pageSize,
            totalPages: Math.ceil(allContacts.length / pageSize),
            totalItems: allContacts.length
        };

        // إضافة معلومات التاريخ
        let updateInfo = '';
        if (lastUpdated) {
            const lastUpdateDate = new Date(lastUpdated);
            const now = new Date();
            const diffMinutes = Math.floor((now - lastUpdateDate) / (1000 * 60));
            const diffHours = Math.floor(diffMinutes / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffDays > 0) {
                updateInfo = `منذ ${diffDays} يوم${diffDays > 1 ? '' : ''}`;
            } else if (diffHours > 0) {
                updateInfo = `منذ ${diffHours} ساعة${diffHours > 1 ? '' : ''}`;
            } else {
                updateInfo = `منذ ${diffMinutes} دقيقة${diffMinutes > 1 ? '' : ''}`;
            }
        }

        // عرض جهات الاتصال
        displayContacts(allContacts, pagination, false, updateInfo);
    }

    // وظيفة إضافة مستمعات الأحداث للصور في النافذة المنبثقة
    function addImageModalEventListeners() {
        // إضافة مستمعات الأحداث للصور
        document.querySelectorAll('.contact-image').forEach(img => {
            img.addEventListener('click', function() {
                const imageUrl = this.getAttribute('data-image-url');
                const contactName = this.getAttribute('data-contact-name');

                // تعيين الصورة والاسم في النافذة المنبثقة
                document.getElementById('modalImage').src = imageUrl;
                document.getElementById('imageModalLabel').textContent = `صورة ${contactName}`;
                document.getElementById('imageInfo').textContent = `${contactName}`;
            });
        });
    }

    // وظيفة لعرض جهات الاتصال في الجدول
    function displayContacts(contacts, pagination, isSearchResult = false, updateInfo = '') {
        const contactsContainer = document.getElementById('contactsContainer');
        if (!contacts || contacts.length === 0) {
            contactsContainer.innerHTML = `
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    ${isSearchResult ? 'لا توجد نتائج تطابق البحث.' : 'لا توجد جهات اتصال متاحة.'}
                </div>
            `;
            return;
        }

        const startIndex = (pagination.page - 1) * pagination.pageSize;
        const endIndex = Math.min(startIndex + pagination.pageSize, contacts.length);
        const displayedContacts = contacts.slice(startIndex, endIndex);

        // إنشاء عناصر التنقل بين الصفحات
        const paginationHTML = pagination.totalPages > 1 ? `
            <nav aria-label="صفحات جهات الاتصال">
                <ul class="pagination justify-content-center">
                    <li class="page-item ${pagination.page === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${pagination.page - 1}" aria-label="السابق">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    ${createPaginationItems(pagination.page, pagination.totalPages)}
                    <li class="page-item ${pagination.page === pagination.totalPages ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${pagination.page + 1}" aria-label="التالي">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        ` : '';

        // إنشاء حقل البحث وأزرار الفرز
        let tableHTML = `
            <div class="mb-3 row align-items-center">
                <div class="col-md-6">
                <div class="input-group">
                        <input type="text" id="searchInput" class="form-control" placeholder="بحث في جهات الاتصال...">
                        <span class="input-group-text" id="search-icon">
                            <i class="fas fa-search"></i>
                        </span>
                </div>
                </div>
                <div class="col-md-6 d-flex align-items-center justify-content-end">
                    ${updateInfo ? `<span class="badge bg-info me-2">آخر تحديث: ${updateInfo}</span>` : ''}
                    <span class="contacts-count badge bg-success me-2">${contacts.length} جهة اتصال</span>
                    <button id="sortByNameBtn" class="btn btn-outline-secondary btn-sm ms-1">
                        <i class="fas fa-sort-alpha-down me-1"></i>الاسم
                        <span id="sortNameIndicator">${sortDirection === 'asc' ? '↓' : '↑'}</span>
                </button>
                </div>
            </div>
        `;

        // إنشاء جدول جهات الاتصال
        tableHTML += `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 50px;">#</th>
                            <th>الاسم</th>
                            <th>رقم الهاتف</th>
                            <th style="width: 100px;">معلومات إضافية</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        // إضافة صفوف جهات الاتصال
        displayedContacts.forEach((contact, index) => {
            const absoluteIndex = startIndex + index + 1;
            let name = contact.name || contact.pushName || contact.savedName || contact.number || 'بدون اسم';
            const phoneNumber = contact.number || (contact.id ? contact.id.replace('@c.us', '') : 'غير متوفر');
            const profilePicUrl = contact.profilePictureUrl;

            // تجميع المعلومات الإضافية
            const additionalInfo = [];
            if (contact.isBusiness) {
                additionalInfo.push('<span class="badge bg-info">حساب أعمال</span>');
            }
            if (contact.verifiedName) {
                additionalInfo.push(`<span class="badge bg-success" title="الاسم الموثق">${contact.verifiedName}</span>`);
            }

            // عرض محتوى غني
            const savedNameDisplay = contact.savedName ? `<small class="text-muted">(${contact.savedName})</small>` : '';
            let nameDisplay = '';

            // عرض الاسم بين قوسين إذا كان الاسم يختلف بين اسم واتساب والاسم المحفوظ
            if (contact.pushName && contact.savedName && contact.pushName !== contact.savedName) {
                nameDisplay = `${contact.pushName} ${savedNameDisplay}`;
            } else {
                nameDisplay = name + (savedNameDisplay && name !== contact.savedName ? ' ' + savedNameDisplay : '');
            }

            // تحديد لون خلفية الصف إذا كان الاتصال مختارًا
            const isSelected = window.selectedItems && window.selectedItems.some(item =>
                item.id === contact.id || (item.number && item.number === phoneNumber)
            );

            tableHTML += `
                <tr class="contact-row${isSelected ? ' selected-row' : ''}" data-id="${contact.id || ''}" data-number="${phoneNumber}">
                    <td>${absoluteIndex}</td>
                    <td>
                        <div class="d-flex align-items-center">
                            ${profilePicUrl ?
                                `<div class="position-relative me-2">
                                    <img src="${profilePicUrl}" alt="${name}" class="contact-image me-1" data-bs-toggle="modal" data-bs-target="#imageModal" data-image-url="${profilePicUrl}" data-contact-name="${name}">
                                    <div class="position-absolute top-0 end-0 d-flex align-items-center justify-content-center"
                                         style="width: 18px; height: 18px; background: rgba(0,0,0,0.5); border-radius: 50%; cursor: pointer;">
                                        <i class="fas fa-search-plus text-white" style="font-size: 10px;"></i>
                                    </div>
                                </div>` :
                                `<div class="contact-image-placeholder me-2 d-flex align-items-center justify-content-center bg-light">
                                    <i class="fas fa-user"></i>
                                </div>`
                            }
                        <div class="contact-name">
                                ${nameDisplay}
                            </div>
                        </div>
                    </td>
                    <td>
                        <a href="https://wa.me/${phoneNumber}" target="_blank" class="text-decoration-none">
                            ${phoneNumber}
                            <i class="fas fa-external-link-alt ms-1 small text-muted"></i>
                        </a>
                    </td>
                    <td class="text-center">
                        ${additionalInfo.length > 0 ? additionalInfo.join(' ') : '-'}
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        // إضافة التنقل بين الصفحات أسفل الجدول
        tableHTML += paginationHTML;

        contactsContainer.innerHTML = tableHTML;

        // إضافة مستمعات الأحداث للصفحات
        if (pagination.totalPages > 1) {
            document.querySelectorAll('.page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const pageNum = parseInt(this.getAttribute('data-page'));
                    if (pageNum >= 1 && pageNum <= pagination.totalPages) {
                        currentPage = pageNum;
                        if (isSearchResult) {
                            // إذا كان هذا نتيجة بحث، استخدم البيانات الحالية
                            displayContacts(contacts, { ...pagination, page: currentPage }, true, updateInfo);
                        } else {
                            // استدعاء الصفحة الجديدة
                            displayContactsPage(currentPage);
                        }
                    }
                });
            });
        }

        // إضافة مستمع الحدث لزر الترتيب حسب الاسم
        document.getElementById('sortByNameBtn').addEventListener('click', function() {
            // تبديل اتجاه الترتيب
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';

            // تحديث مؤشر اتجاه الترتيب
            document.getElementById('sortNameIndicator').textContent = sortDirection === 'asc' ? '↓' : '↑';

            // فرز جهات الاتصال
            const sortedContacts = [...contacts].sort((a, b) => {
                const nameA = a.name || a.pushName || a.savedName || a.number || '';
                const nameB = b.name || b.pushName || b.savedName || b.number || '';

                return sortDirection === 'asc'
                    ? nameA.localeCompare(nameB, 'ar')
                    : nameB.localeCompare(nameA, 'ar');
            });

            // إعادة عرض جهات الاتصال المرتبة
            displayContacts(sortedContacts, { ...pagination, page: 1 }, isSearchResult, updateInfo);
        });

        // إضافة مستمع الحدث لحقل البحث
        const searchInput = document.getElementById('searchInput');
        const searchIcon = document.getElementById('search-icon');

        // وظيفة البحث المؤجلة
        const debouncedSearch = debounce(function() {
            const query = searchInput.value.trim();

            if (query.length > 0) {
                // تغيير أيقونة البحث إلى أيقونة التحميل
                searchIcon.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                // البحث في جهات الاتصال
                searchContacts(query);
            } else {
                // إعادة تعيين أيقونة البحث
                resetSearchIcon();

                // إعادة عرض الصفحة الحالية
                displayContactsPage(currentPage);
            }
        }, 500);

        // مستمع الحدث لحقل البحث
        searchInput.addEventListener('input', debouncedSearch);

        // إضافة مستمع الحدث للنقر على الأيقونة
        searchIcon.addEventListener('click', function() {
            debouncedSearch();
        });

        // إضافة مستمعات الأحداث للصور وصفوف الجدول
        addContactsEventListeners();
        addImageModalEventListeners();
    }

    // وظيفة إضافة مستمعات الأحداث لعناصر جدول جهات الاتصال
    function addContactsEventListeners() {
        // مستمع حدث لزر التحديث
        const refreshBtn = document.getElementById('refreshContactsBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => loadContacts(true));
        }

        // إضافة مستمعات الأحداث لصفوف جهات الاتصال
        document.querySelectorAll('.contact-row').forEach(row => {
            if (!row.hasAttribute('data-has-click-listener')) {
                row.setAttribute('data-has-click-listener', 'true');

                row.addEventListener('click', function(event) {
                    // عدم تنفيذ العملية إذا كان النقر على الصورة أو على الرابط
                    if (event.target.classList.contains('contact-image') ||
                        event.target.tagName === 'A' ||
                        event.target.tagName === 'I' ||
                        event.target.closest('.contact-image') ||
                        event.target.closest('a')) {
                        return;
                    }

                    const contactId = this.getAttribute('data-id');
                    const contactNumber = this.getAttribute('data-number');

                    // استخراج اسم جهة الاتصال من الخلية الأولى (التي تحتوي على الاسم)
                    const nameCell = this.querySelector('td:nth-child(2)');
                    const nameElement = nameCell.querySelector('.contact-name') || nameCell.querySelector('div > div');
                    const contactName = nameElement ? nameElement.textContent.trim() :
                                      (nameCell.textContent.trim() || 'جهة اتصال');

                    // استخراج رابط الصورة إن وجد
                    const imgElement = this.querySelector('.contact-image');
                    const contactImage = imgElement ? imgElement.getAttribute('src') || '' : '';

                    // التحقق من عدم وجود العنصر مسبقًا في القائمة المحددة
                    const exists = window.selectedItems && window.selectedItems.some(item =>
                        (item.id === contactId) || (item.number === contactNumber)
                    );

                    if (!exists) {
                        // إضافة جهة الاتصال إلى القائمة المحددة
                        window.selectedItems.push({
                            id: contactId || '',
                            name: contactName,
                            number: contactNumber,
                            image: contactImage,
                            type: 'contact'
                        });

                        // تحديث جدول العناصر المحددة
                        window.updateSelectedItemsTable();

                        // إضافة صنف "selected-row" لتمييز الصف المحدد
                        this.classList.add('selected-row');

                        // إظهار إشعار
                        Swal.fire({
                            title: 'تمت الإضافة',
                            text: `تمت إضافة ${contactName} إلى القائمة المختارة`,
                            icon: 'success',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 2000
                        });
                    }
                });
            }
        });

        // مستمع حدث للنقر على صور جهات الاتصال لعرضها في نافذة منبثقة
        const contactImages = document.querySelectorAll('.contact-image');
        contactImages.forEach(img => {
            img.addEventListener('click', function() {
                const imageUrl = this.getAttribute('data-image-url');
                const contactName = this.getAttribute('data-contact-name');

                // تعيين عنوان النافذة المنبثقة
                document.getElementById('imageModalLabel').textContent = `صورة ${contactName}`;

                // تعيين مصدر الصورة
                const modalImage = document.getElementById('modalImage');

                // إظهار مؤشر التحميل قبل تحميل الصورة
                modalImage.style.display = 'none';
                const imageContainer = document.querySelector('.image-container');

                // إزالة أي مؤشر تحميل سابق
                const existingSpinner = imageContainer.querySelector('.spinner-border');
                if (existingSpinner) {
                    existingSpinner.remove();
                }

                // إضافة مؤشر التحميل
                const spinner = document.createElement('div');
                spinner.className = 'spinner-border text-success';
                spinner.style.width = '3rem';
                spinner.style.height = '3rem';
                spinner.setAttribute('role', 'status');
                spinner.innerHTML = '<span class="visually-hidden">جاري التحميل...</span>';
                imageContainer.appendChild(spinner);

                // تحميل الصورة بحجمها الحقيقي
                const img = new Image();
                img.setAttribute('crossorigin', 'anonymous'); // للسماح بتحميل الصور من مصادر خارجية

                // متغير لتخزين مستوى التكبير الحالي
                let currentZoom = 1;

                img.onload = function() {
                    // إزالة مؤشر التحميل وإظهار الصورة
                    if (spinner.parentNode) {
                        spinner.parentNode.removeChild(spinner);
                    }

                    // تعيين الصورة بحجمها المناسب للنافذة
                    modalImage.src = imageUrl;
                    modalImage.style.display = 'block';
                    modalImage.style.transform = 'scale(1)';
                    modalImage.style.transformOrigin = 'center center';
                    modalImage.style.cursor = 'grab';
                    modalImage.style.maxWidth = '100%';
                    modalImage.style.maxHeight = '70vh';
                    modalImage.style.objectFit = 'contain';

                    // عرض معلومات الصورة بعد تحميلها
                    const imageInfo = document.getElementById('imageInfo');
                    imageInfo.textContent = 'جاري تحميل معلومات الصورة...';

                    // عند اكتمال تحميل الصورة، عرض أبعادها
                    modalImage.onload = function() {
                        const width = this.naturalWidth;
                        const height = this.naturalHeight;
                        imageInfo.textContent = `الأبعاد: ${width} × ${height} بكسل`;
                    };

                    // إضافة تأثير ظهور تدريجي للصورة
                    modalImage.style.opacity = '0';
                    modalImage.style.transition = 'opacity 0.3s ease-in-out';
                    setTimeout(() => {
                        modalImage.style.opacity = '1';
                    }, 50);

                    // إضافة مستمعات الأحداث لأزرار التكبير والتصغير
                    setupZoomButtons(modalImage);
                };

                img.onerror = function() {
                    // إزالة مؤشر التحميل وإظهار الصورة الافتراضية
                    if (spinner.parentNode) {
                        spinner.parentNode.removeChild(spinner);
                    }

                    // تعيين صورة افتراضية
                    modalImage.src = 'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png';
                    modalImage.style.display = 'block';

                    // تحديث معلومات الصورة
                    const imageInfo = document.getElementById('imageInfo');
                    imageInfo.textContent = 'تعذر تحميل الصورة';

                    // إضافة تأثير ظهور تدريجي للصورة
                    modalImage.style.opacity = '0';
                    modalImage.style.transition = 'opacity 0.3s ease-in-out';
                    setTimeout(() => {
                        modalImage.style.opacity = '1';
                    }, 50);

                    // إضافة مستمعات الأحداث لأزرار التكبير والتصغير
                    setupZoomButtons(modalImage);
                };

                // وظيفة لإعداد أزرار التكبير والتصغير
                function setupZoomButtons(image) {
                    const zoomInBtn = document.getElementById('zoomInBtn');
                    const zoomOutBtn = document.getElementById('zoomOutBtn');
                    const resetZoomBtn = document.getElementById('resetZoomBtn');
                    const imageContainer = document.querySelector('.image-container');

                    // تعيين مستوى التكبير الافتراضي
                    currentZoom = 1;

                    // متغيرات لتتبع موضع الصورة
                    let posX = 0;
                    let posY = 0;
                    let isDragging = false;
                    let startX, startY;

                    // وظيفة التكبير
                    function zoomIn(amount = 0.25) {
                        currentZoom += amount;
                        if (currentZoom > 5) currentZoom = 5; // الحد الأقصى للتكبير
                        updateImageTransform();
                    }

                    // وظيفة التصغير
                    function zoomOut(amount = 0.25) {
                        currentZoom -= amount;
                        if (currentZoom < 0.5) currentZoom = 0.5; // الحد الأدنى للتصغير

                        // إذا كان التصغير يعيد الصورة إلى الحجم الطبيعي، أعد ضبط الموضع
                        if (currentZoom <= 1) {
                            posX = 0;
                            posY = 0;
                        }

                        updateImageTransform();
                    }

                    // وظيفة إعادة الضبط
                    function resetZoom() {
                        currentZoom = 1;
                        posX = 0;
                        posY = 0;
                        updateImageTransform();
                    }

                    // وظيفة تحديث تحويل الصورة
                    function updateImageTransform() {
                        image.style.transform = `scale(${currentZoom}) translate(${posX}px, ${posY}px)`;

                        // تغيير مؤشر الماوس بناءً على مستوى التكبير
                        if (currentZoom > 1) {
                            image.style.cursor = 'grab';
                        } else {
                            image.style.cursor = 'default';
                        }

                        // تحديث معلومات الصورة
                        const imageInfo = document.getElementById('imageInfo');
                        const width = Math.round(image.naturalWidth * currentZoom);
                        const height = Math.round(image.naturalHeight * currentZoom);
                        imageInfo.textContent = `الأبعاد الأصلية: ${image.naturalWidth} × ${image.naturalHeight} بكسل | التكبير: ${Math.round(currentZoom * 100)}%`;
                    }

                    // مستمع حدث لزر التكبير
                    zoomInBtn.onclick = function(e) {
                        e.preventDefault();
                        zoomIn();
                    };

                    // مستمع حدث لزر التصغير
                    zoomOutBtn.onclick = function(e) {
                        e.preventDefault();
                        zoomOut();
                    };

                    // مستمع حدث لزر إعادة الضبط
                    resetZoomBtn.onclick = function(e) {
                        e.preventDefault();
                        resetZoom();
                    };

                    // مستمع حدث لعجلة الماوس للتكبير والتصغير
                    imageContainer.addEventListener('wheel', function(e) {
                        e.preventDefault(); // منع السلوك الافتراضي للمتصفح (التمرير)

                        if (e.deltaY < 0) {
                            // تمرير لأعلى = تكبير
                            zoomIn(0.1);
                        } else {
                            // تمرير لأسفل = تصغير
                            zoomOut(0.1);
                        }
                    });

                    // إضافة دعم التكبير والتصغير باللمس (للأجهزة اللوحية والهواتف)
                    let initialPinchDistance = 0;

                    imageContainer.addEventListener('touchstart', function(e) {
                        if (e.touches.length === 2) {
                            initialPinchDistance = Math.hypot(
                                e.touches[0].pageX - e.touches[1].pageX,
                                e.touches[0].pageY - e.touches[1].pageY
                            );
                        }
                    });

                    imageContainer.addEventListener('touchmove', function(e) {
                        if (e.touches.length === 2) {
                            e.preventDefault();

                            const currentDistance = Math.hypot(
                                e.touches[0].pageX - e.touches[1].pageX,
                                e.touches[0].pageY - e.touches[1].pageY
                            );

                            if (initialPinchDistance > 0) {
                                const pinchChange = currentDistance - initialPinchDistance;
                                if (Math.abs(pinchChange) > 5) {
                                    if (pinchChange > 0) {
                                        zoomIn(0.05);
                                    } else {
                                        zoomOut(0.05);
                                    }
                                    initialPinchDistance = currentDistance;
                                }
                            }
                        }
                    });

                    // إضافة مستمعات الأحداث لتحريك الصورة
                    image.addEventListener('mousedown', function(e) {
                        if (currentZoom > 1) {
                            isDragging = true;
                            startX = e.clientX - posX;
                            startY = e.clientY - posY;
                            image.style.cursor = 'grabbing';
                            e.preventDefault();
                        }
                    });

                    imageContainer.addEventListener('mousemove', function(e) {
                        if (isDragging && currentZoom > 1) {
                            posX = e.clientX - startX;
                            posY = e.clientY - startY;
                            updateImageTransform();
                            e.preventDefault();
                        }
                    });

                    imageContainer.addEventListener('mouseup', function() {
                        if (isDragging) {
                            isDragging = false;
                            image.style.cursor = 'grab';
                        }
                    });

                    imageContainer.addEventListener('mouseleave', function() {
                        if (isDragging) {
                            isDragging = false;
                            image.style.cursor = 'grab';
                        }
                    });

                    // إضافة دعم تحريك الصورة باللمس
                    image.addEventListener('touchstart', function(e) {
                        if (e.touches.length === 1 && currentZoom > 1) {
                            isDragging = true;
                            startX = e.touches[0].clientX - posX;
                            startY = e.touches[0].clientY - posY;
                            e.preventDefault();
                        }
                    });

                    imageContainer.addEventListener('touchmove', function(e) {
                        if (isDragging && e.touches.length === 1 && currentZoom > 1) {
                            posX = e.touches[0].clientX - startX;
                            posY = e.touches[0].clientY - startY;
                            updateImageTransform();
                            e.preventDefault();
                        }
                    });

                    imageContainer.addEventListener('touchend', function() {
                        isDragging = false;
                    });
                }

                img.src = imageUrl;

                // معالجة خطأ تحميل الصورة
                modalImage.onerror = function() {
                    this.src = 'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png';

                    // تحديث معلومات الصورة
                    const imageInfo = document.getElementById('imageInfo');
                    imageInfo.textContent = 'تعذر تحميل الصورة';
                };

                // إضافة مستمع لحدث فتح النافذة المنبثقة
                const imageModal = document.getElementById('imageModal');
                imageModal.addEventListener('shown.bs.modal', function() {
                    // التركيز على زر الإغلاق عند فتح النافذة المنبثقة
                    document.querySelector('#imageModal .btn-close').focus();
                });
            });
        });

        // مستمع حدث لتغيير حجم الصفحة
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', function() {
                pageSize = parseInt(this.value);
                currentPage = 1; // العودة إلى الصفحة الأولى

                // إذا كان هناك نص بحث حالي، قم بإعادة البحث
                if (currentSearchQuery) {
                    searchContacts(currentSearchQuery);
                } else if (allContacts.length > 0) {
                    // إذا كانت جميع جهات الاتصال محملة بالفعل
                    displayContactsPage(currentPage);
                } else {
                    // إعادة تحميل جهات الاتصال من الخادم
                    loadContacts();
                }
            });
        }

        // مستمع حدث لحقل البحث
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            // إنشاء وظيفة البحث المؤجلة باستخدام debounce
            const debouncedSearch = debounce(function(value) {
                // عرض مؤشر البحث
                const searchIcon = searchInput.previousElementSibling.querySelector('i');
                if (searchIcon) {
                    searchIcon.className = 'fas fa-spinner fa-spin';
                }

                // تنفيذ البحث
                searchContacts(value);
            }, 500); // انتظار 500 مللي ثانية بعد توقف المستخدم عن الكتابة

            // تنفيذ البحث عند الضغط على Enter
            searchInput.addEventListener('keydown', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault(); // منع السلوك الافتراضي للمتصفح
                    searchContacts(this.value);
                }

                // السماح بالخروج من الحقل عند الضغط على مفتاح Escape
                if (event.key === 'Escape') {
                    this.blur();
                }
            });

            // تنفيذ البحث بعد توقف المستخدم عن الكتابة باستخدام debounce
            searchInput.addEventListener('input', function() {
                // استخدام وظيفة البحث المؤجلة
                debouncedSearch(this.value);
            });

            // إعادة أيقونة البحث إلى حالتها الطبيعية عند فقدان التركيز
            searchInput.addEventListener('blur', function() {
                // إعادة أيقونة البحث إلى حالتها الطبيعية
                resetSearchIcon();
            });

        }

        // مستمع حدث لزر البحث
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', function() {
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchContacts(searchInput.value);
                }
            });
        }

        // مستمع حدث لزر مسح البحث
        const clearSearchBtn = document.getElementById('clearSearchBtn');
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', function() {
                // إعادة تعيين حقل البحث
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.value = '';

                    // إعادة عرض جميع جهات الاتصال
                    displayContactsPage(1);

                    // إعادة التركيز إلى حقل البحث بعد مسح البحث
                    setTimeout(() => {
                        searchInput.focus();
                    }, 10);
                }
            });
        }

        // مستمع حدث لأزرار التنقل بين الصفحات
        const pageLinks = document.querySelectorAll('.pagination .page-link');
        pageLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                const page = this.dataset.page;

                if (page === 'prev') {
                    if (currentPage > 1) {
                        currentPage--;
                    }
                } else if (page === 'next') {
                    if (currentPage < Math.ceil(allContacts.length / pageSize)) {
                        currentPage++;
                    }
                } else {
                    currentPage = parseInt(page);
                }

                // عرض الصفحة الجديدة
                displayContactsPage(currentPage);
            });
        });

        // مستمع حدث لترتيب جهات الاتصال حسب الاسم
        const nameHeader = document.getElementById('nameHeader');
        if (nameHeader) {
            nameHeader.addEventListener('click', function() {
                // تبديل اتجاه الترتيب
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';

                // تحديث أيقونة الترتيب
                const icon = this.querySelector('i');
                icon.className = sortDirection === 'asc' ? 'fas fa-sort-up ms-1' : 'fas fa-sort-down ms-1';

                // ترتيب جهات الاتصال
                allContacts.sort((a, b) => {
                    const nameA = a.name || '';
                    const nameB = b.name || '';

                    if (sortDirection === 'asc') {
                        return nameA.localeCompare(nameB);
                    } else {
                        return nameB.localeCompare(nameA);
                    }
                });

                // عرض الصفحة الحالية بعد الترتيب
                displayContactsPage(currentPage);
            });
        }
    }

    // وظيفة تحديث حالة الاتصال
    function updateConnectionStatus(connected, status) {
        const statusBadge = document.getElementById('statusBadge');

        if (connected && status === 'connected') {
            statusBadge.innerHTML = '<i class="fas fa-circle text-success me-2"></i>متصل';
            statusBadge.className = 'status-badge connected';
        } else {
            statusBadge.innerHTML = '<i class="fas fa-circle text-danger me-2"></i>غير متصل';
            statusBadge.className = 'status-badge disconnected';
        }
    }

    // وظيفة عرض التنبيهات
    function showAlert(message, type) {
        Swal.fire({
            title: type === 'success' ? 'نجاح' : type === 'warning' ? 'تنبيه' : 'خطأ',
            text: message,
            icon: type,
            confirmButtonText: 'حسنًا',
            confirmButtonColor: '#25D366'
        });
    }

    // وظيفة إنشاء عناصر التنقل بين الصفحات
    function createPaginationItems(currentPage, totalPages) {
        let paginationItems = '';
        const maxVisiblePages = 5; // أقصى عدد من الصفحات للعرض

        // تحديد نطاق الصفحات التي سيتم عرضها
        let startPage, endPage;

        if (totalPages <= maxVisiblePages) {
            // إذا كان إجمالي الصفحات أقل من أو يساوي الحد الأقصى، عرض كل الصفحات
            startPage = 1;
            endPage = totalPages;
        } else {
            // حساب نصف عدد الصفحات المرئية
            const halfVisiblePages = Math.floor(maxVisiblePages / 2);

            // حالات مختلفة للصفحة الحالية
            if (currentPage <= halfVisiblePages + 1) {
                // إذا كانت الصفحة الحالية قريبة من البداية
                startPage = 1;
                endPage = maxVisiblePages;
            } else if (currentPage >= totalPages - halfVisiblePages) {
                // إذا كانت الصفحة الحالية قريبة من النهاية
                startPage = totalPages - maxVisiblePages + 1;
                endPage = totalPages;
            } else {
                // الصفحة الحالية في المنتصف
                startPage = currentPage - halfVisiblePages;
                endPage = currentPage + halfVisiblePages;
            }
        }

        // إنشاء عناصر HTML للصفحات
        for (let i = startPage; i <= endPage; i++) {
            paginationItems += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        }

        return paginationItems;
    }
});

