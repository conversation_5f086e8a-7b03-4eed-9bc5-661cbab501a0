/**
 * وظائف مساعدة لإدارة الملفات المؤقتة لتخزين بيانات الاتصال
 * هذا الملف يحتوي على وظائف لتخزين واسترجاع وتنظيف بيانات الاتصال في ملفات مؤقتة
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto'); // مكتبة مضمنة في Node.js لإنشاء معرفات فريدة

// مجلد للملفات المؤقتة
const TEMP_DIR = path.join(__dirname, 'temp');

// التأكد من وجود المجلد
if (!fs.existsSync(TEMP_DIR)) {
    fs.mkdirSync(TEMP_DIR, { recursive: true });
}

/**
 * إنشاء معرف فريد للعملية
 * @returns {string} - معرف فريد
 */
function generateOperationId() {
    return crypto.randomBytes(16).toString('hex');
}

/**
 * تخزين بيانات الاتصال في ملف مؤقت
 * @param {string} accountName - اسم الحساب
 * @param {object} connection - كائن الاتصال
 * @returns {string} - معرف العملية
 */
function storeConnectionData(accountName, connection) {
    // إنشاء معرف فريد للعملية
    const operationId = generateOperationId();

    // إنشاء كائن البيانات
    const data = {
        accountName,
        status: connection.status,
        timestamp: Date.now()
    };

    // تخزين البيانات في ملف مؤقت
    const filePath = path.join(TEMP_DIR, `${operationId}.json`);
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));

    console.log(`تم تخزين بيانات الاتصال للحساب ${accountName} بمعرف عملية ${operationId}`);
    return operationId;
}

/**
 * استرجاع بيانات الاتصال من الملف المؤقت
 * @param {string} operationId - معرف العملية
 * @returns {object} - بيانات الاتصال
 */
function getConnectionData(operationId) {
    const filePath = path.join(TEMP_DIR, `${operationId}.json`);

    console.log(`محاولة قراءة بيانات العملية ${operationId} من ${filePath}`);

    if (!fs.existsSync(filePath)) {
        console.error(`ملف العملية غير موجود: ${filePath}`);
        // بدلاً من رمي خطأ، نعيد بيانات مبدئية
        return {
            operationId,
            startTime: Date.now(),
            status: 'processing',
            success: null,
            successCount: 0,
            failureCount: 0,
            recipientsCount: 0,
            results: [],
            error: `لا توجد بيانات للعملية ${operationId}`
        };
    }

    try {
        // قراءة البيانات من الملف
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        console.log(`تم قراءة بيانات العملية ${operationId} بنجاح`);
        return data;
    } catch (error) {
        console.error(`خطأ في قراءة بيانات العملية ${operationId}:`, error);
        // بدلاً من رمي خطأ، نعيد بيانات مبدئية مع رسالة الخطأ
        return {
            operationId,
            startTime: Date.now(),
            status: 'failed',
            success: false,
            successCount: 0,
            failureCount: 0,
            recipientsCount: 0,
            results: [],
            error: `فشل في قراءة بيانات العملية ${operationId}: ${error.message}`
        };
    }
}

/**
 * تحديث بيانات الاتصال في الملف المؤقت
 * @param {string} operationId - معرف العملية
 * @param {object} newData - البيانات الجديدة المراد إضافتها
 */
function updateConnectionData(operationId, newData) {
    const filePath = path.join(TEMP_DIR, `${operationId}.json`);

    if (!fs.existsSync(filePath)) {
        throw new Error(`لا توجد بيانات للعملية ${operationId}`);
    }

    // قراءة البيانات الحالية
    const currentData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

    // دمج البيانات الحالية مع البيانات الجديدة
    const updatedData = { ...currentData, ...newData, lastUpdated: Date.now() };

    // حفظ البيانات المحدثة
    fs.writeFileSync(filePath, JSON.stringify(updatedData, null, 2));

    console.log(`تم تحديث بيانات العملية ${operationId}`);
    return updatedData;
}

/**
 * إضافة نتيجة إرسال رسالة إلى ملف البيانات المؤقت
 * @param {string} operationId - معرف العملية
 * @param {object} result - نتيجة إرسال الرسالة
 */
function addMessageResult(operationId, result) {
    const filePath = path.join(TEMP_DIR, `${operationId}.json`);

    if (!fs.existsSync(filePath)) {
        throw new Error(`لا توجد بيانات للعملية ${operationId}`);
    }

    // قراءة البيانات الحالية
    const currentData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

    // إضافة النتيجة إلى مصفوفة النتائج
    if (!currentData.results) {
        currentData.results = [];
    }

    currentData.results.push(result);
    currentData.lastUpdated = Date.now();

    // حفظ البيانات المحدثة
    fs.writeFileSync(filePath, JSON.stringify(currentData, null, 2));

    console.log(`تم إضافة نتيجة جديدة للعملية ${operationId}`);
    return currentData;
}

/**
 * حذف ملف البيانات المؤقت
 * @param {string} operationId - معرف العملية
 */
function cleanupConnectionData(operationId) {
    const filePath = path.join(TEMP_DIR, `${operationId}.json`);

    if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`تم حذف ملف البيانات المؤقت للعملية ${operationId}`);
        return true;
    }

    console.log(`لم يتم العثور على ملف بيانات للعملية ${operationId}`);
    return false;
}

/**
 * تنظيف جميع الملفات المؤقتة القديمة (أقدم من ساعة واحدة)
 */
function cleanupOldTempFiles() {
    const files = fs.readdirSync(TEMP_DIR);
    const oneHourAgo = Date.now() - (60 * 60 * 1000);

    let cleanedCount = 0;

    for (const file of files) {
        if (file.endsWith('.json')) {
            const filePath = path.join(TEMP_DIR, file);
            const stats = fs.statSync(filePath);

            // حذف الملفات الأقدم من ساعة واحدة
            if (stats.mtimeMs < oneHourAgo) {
                fs.unlinkSync(filePath);
                cleanedCount++;
            }
        }
    }

    if (cleanedCount > 0) {
        console.log(`تم تنظيف ${cleanedCount} ملفات مؤقتة قديمة`);
    }

    return cleanedCount;
}

/**
 * حفظ بيانات العملية في ملف مؤقت
 * @param {string} operationId - معرف العملية
 * @param {object} data - بيانات العملية
 * @returns {object} - البيانات المحفوظة
 */
function saveConnectionData(operationId, data) {
    const filePath = path.join(TEMP_DIR, `${operationId}.json`);

    try {
        // إضافة وقت الإنشاء إذا لم يكن موجوداً
        if (!data.createdAt) {
            data.createdAt = Date.now();
        }

        // حفظ البيانات في الملف
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
        console.log(`تم حفظ بيانات العملية ${operationId} بنجاح`);
        return data;
    } catch (error) {
        console.error(`خطأ في حفظ بيانات العملية ${operationId}:`, error);
        throw new Error(`فشل في حفظ بيانات العملية ${operationId}: ${error.message}`);
    }
}

/**
 * تحديث حالة اكتمال العملية
 * @param {string} operationId - معرف العملية
 * @param {number} totalCount - العدد الإجمالي للعمليات
 * @param {number} successCount - عدد العمليات الناجحة
 * @param {number} failCount - عدد العمليات الفاشلة
 * @param {array} results - نتائج العمليات
 * @param {string} error - رسالة الخطأ (اختياري)
 */
function updateOperationCompletionStatus(operationId, totalCount, successCount, failCount, results, error = null) {
    try {
        const filePath = path.join(TEMP_DIR, `${operationId}.json`);

        if (!fs.existsSync(filePath)) {
            console.error(`ملف العملية غير موجود: ${filePath}`);
            throw new Error(`لا توجد بيانات للعملية ${operationId}`);
        }

        // قراءة البيانات الحالية
        const currentData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

        // تحديث البيانات
        const updatedData = {
            ...currentData,
            status: error ? 'failed' : (successCount === totalCount ? 'completed' : 'processing'),
            endTime: Date.now(),
            totalCount,
            successCount,
            failCount,
            results,
            error: error || null,
            duration: Date.now() - currentData.startTime,
            success: error ? false : (successCount === totalCount)
        };

        // حفظ البيانات المحدثة
        fs.writeFileSync(filePath, JSON.stringify(updatedData, null, 2));

        console.log(`تم تحديث حالة العملية ${operationId}: ${updatedData.status} (نجاح: ${successCount}/${totalCount})`);
        return updatedData;
    } catch (error) {
        console.error(`خطأ في تحديث حالة العملية ${operationId}:`, error);
        throw error;
    }
}

/**
 * تجميع إحصائيات جميع العمليات وإنشاء ملخص شامل
 * @returns {Object} ملخص شامل لجميع العمليات
 */
function generateOverallSummary() {
    try {
        const summaryData = {
            totalOperations: 0,
            totalRecipients: 0,
            totalMessages: 0,
            successfulMessages: 0,
            failedMessages: 0,
            recipientsSummary: {}, // { recipientId: { name, totalMessages, successfulMessages, failedMessages, messageTypes: [] } }
            operationsSummary: [], // قائمة بجميع العمليات
            generatedAt: new Date().toISOString()
        };

        // قراءة جميع ملفات العمليات
        if (!fs.existsSync(TEMP_DIR)) {
            console.log('مجلد temp غير موجود، لا توجد عمليات للتجميع');
            return summaryData;
        }

        const files = fs.readdirSync(TEMP_DIR).filter(file => file.endsWith('.json'));
        console.log(`تم العثور على ${files.length} ملف عملية للتجميع`);

        files.forEach(file => {
            try {
                const filePath = path.join(TEMP_DIR, file);
                const operationData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

                if (!operationData.operationId) return; // تجاهل الملفات غير الصحيحة

                summaryData.totalOperations++;

                // إضافة معلومات العملية
                const operationSummary = {
                    operationId: operationData.operationId,
                    messageType: operationData.messageType || 'unknown',
                    status: operationData.status || 'unknown',
                    recipientsCount: operationData.recipientsCount || 0,
                    successCount: operationData.successCount || 0,
                    failureCount: operationData.failureCount || 0,
                    startTime: operationData.startTime
                };
                summaryData.operationsSummary.push(operationSummary);

                // معالجة النتائج لكل مستلم
                if (operationData.results && Array.isArray(operationData.results)) {
                    operationData.results.forEach(result => {
                        const recipientId = result.recipient?.id || result.recipient?.name || 'unknown';
                        const recipientName = result.recipient?.name || result.recipient?.id || 'unknown';

                        // تهيئة بيانات المستلم إذا لم تكن موجودة
                        if (!summaryData.recipientsSummary[recipientId]) {
                            summaryData.recipientsSummary[recipientId] = {
                                name: recipientName,
                                totalMessages: 0,
                                successfulMessages: 0,
                                failedMessages: 0,
                                messageTypes: []
                            };
                        }

                        const recipient = summaryData.recipientsSummary[recipientId];

                        // حساب عدد الرسائل بناءً على نوع العملية
                        if (operationData.messageType === 'folder') {
                            // للمجلدات: عدد الملفات الناجحة والفاشلة
                            const successCount = result.successCount || 0;
                            const failCount = result.failCount || 0;

                            recipient.totalMessages += (successCount + failCount);
                            recipient.successfulMessages += successCount;
                            recipient.failedMessages += failCount;

                            summaryData.totalMessages += (successCount + failCount);
                            summaryData.successfulMessages += successCount;
                            summaryData.failedMessages += failCount;
                        } else {
                            // للرسائل العادية: رسالة واحدة
                            recipient.totalMessages += 1;
                            summaryData.totalMessages += 1;

                            if (result.success) {
                                recipient.successfulMessages += 1;
                                summaryData.successfulMessages += 1;
                            } else {
                                recipient.failedMessages += 1;
                                summaryData.failedMessages += 1;
                            }
                        }

                        // إضافة نوع الرسالة إذا لم يكن موجوداً
                        if (!recipient.messageTypes.includes(operationData.messageType)) {
                            recipient.messageTypes.push(operationData.messageType);
                        }
                    });
                }

            } catch (error) {
                console.error(`خطأ في معالجة ملف العملية ${file}:`, error);
            }
        });

        // حساب عدد المستلمين الفريدين
        summaryData.totalRecipients = Object.keys(summaryData.recipientsSummary).length;

        // حفظ الملخص في ملف منفصل
        const summaryPath = path.join(TEMP_DIR, 'overall_summary.json');
        fs.writeFileSync(summaryPath, JSON.stringify(summaryData, null, 2));

        console.log(`تم إنشاء ملخص شامل في: ${summaryPath}`);
        return summaryData;

    } catch (error) {
        console.error('خطأ في إنشاء الملخص الشامل:', error);
        return null;
    }
}

/**
 * عرض الملخص الشامل في الـ terminal
 */
function displayOverallSummary() {
    const summary = generateOverallSummary();

    if (!summary) {
        console.log('❌ فشل في إنشاء الملخص الشامل');
        return;
    }

    console.log(`\n🎉 ===== الملخص الشامل لجميع العمليات =====`);
    console.log(`📊 إجمالي العمليات: ${summary.totalOperations}`);
    console.log(`📨 إجمالي الرسائل المرسلة: ${summary.successfulMessages} من ${summary.totalMessages} رسالة`);
    console.log(`👥 إجمالي المستلمين: ${summary.totalRecipients} مستلم`);

    if (summary.totalMessages > 0) {
        const successRate = ((summary.successfulMessages / summary.totalMessages) * 100).toFixed(1);
        console.log(`📊 معدل النجاح: ${successRate}%`);
    }

    console.log(`\n📋 تفاصيل المستلمين:`);
    Object.entries(summary.recipientsSummary).forEach(([recipientId, data]) => {
        const successRate = data.totalMessages > 0 ? ((data.successfulMessages / data.totalMessages) * 100).toFixed(1) : '0';
        console.log(`  👤 ${data.name}: ${data.successfulMessages}/${data.totalMessages} رسالة (${successRate}%) - أنواع: ${data.messageTypes.join(', ')}`);
    });

    console.log(`\n⏰ تم إنشاء الملخص في: ${new Date(summary.generatedAt).toLocaleString('ar-SA')}`);
    console.log(`=======================================\n`);

    return summary;
}

/**
 * تنظيف الملفات القديمة وإنشاء ملخص نهائي
 * @param {number} maxAgeHours - عمر الملفات بالساعات (افتراضي: 24 ساعة)
 */
function cleanupAndSummarize(maxAgeHours = 24) {
    try {
        // إنشاء ملخص شامل قبل التنظيف
        const summary = displayOverallSummary();

        // تنظيف الملفات القديمة
        if (!fs.existsSync(TEMP_DIR)) return summary;

        const files = fs.readdirSync(TEMP_DIR);
        const maxAge = maxAgeHours * 60 * 60 * 1000; // تحويل إلى ميلي ثانية
        const now = Date.now();
        let cleanedCount = 0;

        files.forEach(file => {
            if (file === 'overall_summary.json') return; // لا تحذف ملف الملخص

            const filePath = path.join(TEMP_DIR, file);
            const stats = fs.statSync(filePath);

            if (now - stats.mtime.getTime() > maxAge) {
                fs.unlinkSync(filePath);
                cleanedCount++;
            }
        });

        if (cleanedCount > 0) {
            console.log(`🧹 تم تنظيف ${cleanedCount} ملف قديم من مجلد temp`);
        }

        return summary;

    } catch (error) {
        console.error('خطأ في عملية التنظيف والتلخيص:', error);
        return null;
    }
}

module.exports = {
    storeConnectionData,
    getConnectionData,
    updateConnectionData,
    addMessageResult,
    cleanupConnectionData,
    cleanupOldTempFiles,
    saveConnectionData,
    updateOperationCompletionStatus,
    generateOverallSummary,
    displayOverallSummary,
    cleanupAndSummarize
};