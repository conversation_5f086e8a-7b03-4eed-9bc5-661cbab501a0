/**
 * وظائف مساعدة لإدارة الملفات المؤقتة لتخزين بيانات الاتصال
 * هذا الملف يحتوي على وظائف لتخزين واسترجاع وتنظيف بيانات الاتصال في ملفات مؤقتة
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto'); // مكتبة مضمنة في Node.js لإنشاء معرفات فريدة

// مجلد للملفات المؤقتة
const TEMP_DIR = path.join(__dirname, 'temp');

// التأكد من وجود المجلد
if (!fs.existsSync(TEMP_DIR)) {
    fs.mkdirSync(TEMP_DIR, { recursive: true });
}

/**
 * إنشاء معرف فريد للعملية
 * @returns {string} - معرف فريد
 */
function generateOperationId() {
    return crypto.randomBytes(16).toString('hex');
}

/**
 * تخزين بيانات الاتصال في ملف مؤقت
 * @param {string} accountName - اسم الحساب
 * @param {object} connection - كائن الاتصال
 * @returns {string} - معرف العملية
 */
function storeConnectionData(accountName, connection) {
    // إنشاء معرف فريد للعملية
    const operationId = generateOperationId();

    // إنشاء كائن البيانات
    const data = {
        accountName,
        status: connection.status,
        timestamp: Date.now()
    };

    // تخزين البيانات في ملف مؤقت
    const filePath = path.join(TEMP_DIR, `${operationId}.json`);
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));

    console.log(`تم تخزين بيانات الاتصال للحساب ${accountName} بمعرف عملية ${operationId}`);
    return operationId;
}

/**
 * استرجاع بيانات الاتصال من الملف المؤقت
 * @param {string} operationId - معرف العملية
 * @returns {object} - بيانات الاتصال
 */
function getConnectionData(operationId) {
    const filePath = path.join(TEMP_DIR, `${operationId}.json`);

    console.log(`محاولة قراءة بيانات العملية ${operationId} من ${filePath}`);

    if (!fs.existsSync(filePath)) {
        console.error(`ملف العملية غير موجود: ${filePath}`);
        // بدلاً من رمي خطأ، نعيد بيانات مبدئية
        return {
            operationId,
            startTime: Date.now(),
            status: 'processing',
            success: null,
            successCount: 0,
            failureCount: 0,
            recipientsCount: 0,
            results: [],
            error: `لا توجد بيانات للعملية ${operationId}`
        };
    }

    try {
        // قراءة البيانات من الملف
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        console.log(`تم قراءة بيانات العملية ${operationId} بنجاح`);
        return data;
    } catch (error) {
        console.error(`خطأ في قراءة بيانات العملية ${operationId}:`, error);
        // بدلاً من رمي خطأ، نعيد بيانات مبدئية مع رسالة الخطأ
        return {
            operationId,
            startTime: Date.now(),
            status: 'failed',
            success: false,
            successCount: 0,
            failureCount: 0,
            recipientsCount: 0,
            results: [],
            error: `فشل في قراءة بيانات العملية ${operationId}: ${error.message}`
        };
    }
}

/**
 * تحديث بيانات الاتصال في الملف المؤقت
 * @param {string} operationId - معرف العملية
 * @param {object} newData - البيانات الجديدة المراد إضافتها
 */
function updateConnectionData(operationId, newData) {
    const filePath = path.join(TEMP_DIR, `${operationId}.json`);

    if (!fs.existsSync(filePath)) {
        throw new Error(`لا توجد بيانات للعملية ${operationId}`);
    }

    // قراءة البيانات الحالية
    const currentData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

    // دمج البيانات الحالية مع البيانات الجديدة
    const updatedData = { ...currentData, ...newData, lastUpdated: Date.now() };

    // حفظ البيانات المحدثة
    fs.writeFileSync(filePath, JSON.stringify(updatedData, null, 2));

    console.log(`تم تحديث بيانات العملية ${operationId}`);
    return updatedData;
}

/**
 * إضافة نتيجة إرسال رسالة إلى ملف البيانات المؤقت
 * @param {string} operationId - معرف العملية
 * @param {object} result - نتيجة إرسال الرسالة
 */
function addMessageResult(operationId, result) {
    const filePath = path.join(TEMP_DIR, `${operationId}.json`);

    if (!fs.existsSync(filePath)) {
        throw new Error(`لا توجد بيانات للعملية ${operationId}`);
    }

    // قراءة البيانات الحالية
    const currentData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

    // إضافة النتيجة إلى مصفوفة النتائج
    if (!currentData.results) {
        currentData.results = [];
    }

    currentData.results.push(result);
    currentData.lastUpdated = Date.now();

    // حفظ البيانات المحدثة
    fs.writeFileSync(filePath, JSON.stringify(currentData, null, 2));

    console.log(`تم إضافة نتيجة جديدة للعملية ${operationId}`);
    return currentData;
}

/**
 * حذف ملف البيانات المؤقت
 * @param {string} operationId - معرف العملية
 */
function cleanupConnectionData(operationId) {
    const filePath = path.join(TEMP_DIR, `${operationId}.json`);

    if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`تم حذف ملف البيانات المؤقت للعملية ${operationId}`);
        return true;
    }

    console.log(`لم يتم العثور على ملف بيانات للعملية ${operationId}`);
    return false;
}

/**
 * تنظيف جميع الملفات المؤقتة القديمة (أقدم من ساعة واحدة)
 */
function cleanupOldTempFiles() {
    const files = fs.readdirSync(TEMP_DIR);
    const oneHourAgo = Date.now() - (60 * 60 * 1000);

    let cleanedCount = 0;

    for (const file of files) {
        if (file.endsWith('.json')) {
            const filePath = path.join(TEMP_DIR, file);
            const stats = fs.statSync(filePath);

            // حذف الملفات الأقدم من ساعة واحدة
            if (stats.mtimeMs < oneHourAgo) {
                fs.unlinkSync(filePath);
                cleanedCount++;
            }
        }
    }

    if (cleanedCount > 0) {
        console.log(`تم تنظيف ${cleanedCount} ملفات مؤقتة قديمة`);
    }

    return cleanedCount;
}

/**
 * حفظ بيانات العملية في ملف مؤقت
 * @param {string} operationId - معرف العملية
 * @param {object} data - بيانات العملية
 * @returns {object} - البيانات المحفوظة
 */
function saveConnectionData(operationId, data) {
    const filePath = path.join(TEMP_DIR, `${operationId}.json`);

    try {
        // إضافة وقت الإنشاء إذا لم يكن موجوداً
        if (!data.createdAt) {
            data.createdAt = Date.now();
        }

        // حفظ البيانات في الملف
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
        console.log(`تم حفظ بيانات العملية ${operationId} بنجاح`);
        return data;
    } catch (error) {
        console.error(`خطأ في حفظ بيانات العملية ${operationId}:`, error);
        throw new Error(`فشل في حفظ بيانات العملية ${operationId}: ${error.message}`);
    }
}

/**
 * تحديث حالة اكتمال العملية
 * @param {string} operationId - معرف العملية
 * @param {number} totalCount - العدد الإجمالي للعمليات
 * @param {number} successCount - عدد العمليات الناجحة
 * @param {number} failCount - عدد العمليات الفاشلة
 * @param {array} results - نتائج العمليات
 * @param {string} error - رسالة الخطأ (اختياري)
 */
function updateOperationCompletionStatus(operationId, totalCount, successCount, failCount, results, error = null) {
    try {
        const filePath = path.join(TEMP_DIR, `${operationId}.json`);

        if (!fs.existsSync(filePath)) {
            console.error(`ملف العملية غير موجود: ${filePath}`);
            throw new Error(`لا توجد بيانات للعملية ${operationId}`);
        }

        // قراءة البيانات الحالية
        const currentData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

        // تحديث البيانات
        const updatedData = {
            ...currentData,
            status: error ? 'failed' : (successCount === totalCount ? 'completed' : 'processing'),
            endTime: Date.now(),
            totalCount,
            successCount,
            failCount,
            results,
            error: error || null,
            duration: Date.now() - currentData.startTime,
            success: error ? false : (successCount === totalCount)
        };

        // حفظ البيانات المحدثة
        fs.writeFileSync(filePath, JSON.stringify(updatedData, null, 2));

        console.log(`تم تحديث حالة العملية ${operationId}: ${updatedData.status} (نجاح: ${successCount}/${totalCount})`);
        return updatedData;
    } catch (error) {
        console.error(`خطأ في تحديث حالة العملية ${operationId}:`, error);
        throw error;
    }
}

/**
 * تحديث ملف الإحصائيات فوري<|im_start|> مع كل رسالة
 * @param {string} sessionId - معرف الجلسة
 * @param {string} recipientId - معرف المستلم (مع @c.us)
 * @param {string} messageType - نوع الرسالة
 * @param {boolean} success - نجح الإرسال أم لا
 * @param {string} error - رسالة الخطأ (إن وجدت)
 * @param {string} operationId - معرف العملية
 * @param {Object} additionalData - بيانات إضافية (للمجلدات مثلاً)
 * @returns {boolean} هل انتهت جميع الرسائل؟
 */
function updateStatisticsFile(sessionId, recipientId, messageType, success, error = null, operationId = null, additionalData = {}) {
    try {
        const statisticsPath = path.join(TEMP_DIR, `statistics_${sessionId}.json`);

        if (!fs.existsSync(statisticsPath)) {
            console.error(`ملف الإحصائيات غير موجود: ${statisticsPath}`);
            return false;
        }

        const statisticsData = JSON.parse(fs.readFileSync(statisticsPath, 'utf8'));

        // محاولة العثور على المعرف الصحيح للمستلم
        let fullRecipientId = null;

        // أولاً: محاولة استخدام المعرف كما هو إذا كان كاملاً
        if (recipientId.includes('@g.us') || recipientId.includes('@c.us')) {
            if (statisticsData.recipients[recipientId]) {
                fullRecipientId = recipientId;
            }
        }

        // ثانياً: إذا لم نجد المعرف، نحاول قراءة المعرف الصحيح من ملف العملية
        if (!fullRecipientId && operationId) {
            try {
                const operationPath = path.join(__dirname, 'temp', `${operationId}.json`);
                if (fs.existsSync(operationPath)) {
                    const operationData = JSON.parse(fs.readFileSync(operationPath, 'utf8'));

                    // البحث عن المعرف الصحيح في نتائج العملية
                    if (operationData.results && operationData.results.length > 0) {
                        const actualRecipientId = operationData.results[0].recipient;
                        if (actualRecipientId && statisticsData.recipients[actualRecipientId]) {
                            fullRecipientId = actualRecipientId;
                            console.log(`🔧 تم العثور على المعرف الصحيح من ملف العملية: ${actualRecipientId}`);
                        }
                    }
                }
            } catch (error) {
                console.error(`خطأ في قراءة ملف العملية ${operationId}:`, error);
            }
        }

        // ثالثاً: إذا لم نجد المعرف بعد، نحاول البحث بالطرق التقليدية
        if (!fullRecipientId) {
            const possibleContactId = recipientId.includes('@c.us') ? recipientId : `${recipientId}@c.us`;
            const possibleGroupId = recipientId.includes('@g.us') ? recipientId : `${recipientId}@g.us`;

            if (statisticsData.recipients[possibleContactId]) {
                fullRecipientId = possibleContactId;
            } else if (statisticsData.recipients[possibleGroupId]) {
                fullRecipientId = possibleGroupId;
            }
        }

        // رابعاً: البحث بالاسم أو الفهرس إذا لم نجد المعرف
        if (!fullRecipientId) {
            // البحث بالاسم
            for (const [existingId, recipientData] of Object.entries(statisticsData.recipients)) {
                if (recipientData.name === recipientId || recipientData.originalId === recipientId) {
                    fullRecipientId = existingId;
                    console.log(`🔧 تم العثور على المعرف بالاسم: ${existingId}`);
                    break;
                }
            }
        }

        if (!fullRecipientId) {
            console.error(`❌ لم يتم العثور على المستلم في الإحصائيات: ${recipientId}`);
            console.log(`📋 المستلمين المتاحين:`, Object.keys(statisticsData.recipients));
            console.log(`🔍 معرف البحث الأصلي: ${recipientId}`);
            console.log(`📁 معرف العملية: ${operationId}`);
            return false;
        }

        console.log(`✅ تم العثور على المستلم: ${fullRecipientId}`);

        const recipient = statisticsData.recipients[fullRecipientId];

        // تحديث حالة الرسالة
        if (recipient.messageTypes[messageType]) {
            const wasCompleted = recipient.messageTypes[messageType].status === 'completed';

            if (messageType === 'folder') {
                // للمجلدات: معالجة خاصة
                const successCount = additionalData.successCount || 0;
                const failCount = additionalData.failCount || 0;

                recipient.messageTypes[messageType] = {
                    status: 'completed',
                    success: successCount > 0,
                    error: successCount === 0 ? 'فشل إرسال جميع الملفات' : null,
                    timestamp: new Date().toISOString(),
                    operationId: operationId,
                    filesCount: successCount + failCount,
                    successfulFiles: successCount,
                    failedFiles: failCount
                };

                // تحديث العدادات فقط إذا لم تكن مكتملة من قبل
                if (!wasCompleted) {
                    recipient.completedMessages++;
                    recipient.successfulMessages += successCount;
                    recipient.failedMessages += failCount;
                }
            } else {
                // للرسائل العادية
                recipient.messageTypes[messageType] = {
                    status: 'completed',
                    success: success,
                    error: error,
                    timestamp: new Date().toISOString(),
                    operationId: operationId,
                    messageId: additionalData.messageId || null
                };

                // تحديث العدادات فقط إذا لم تكن مكتملة من قبل
                if (!wasCompleted) {
                    recipient.completedMessages++;
                    if (success) {
                        recipient.successfulMessages++;
                    } else {
                        recipient.failedMessages++;
                    }
                }
            }
        }

        // حساب إجمالي الرسائل المكتملة
        let totalCompletedMessages = 0;
        Object.values(statisticsData.recipients).forEach(recipientData => {
            totalCompletedMessages += recipientData.completedMessages;
        });

        // التحقق من اكتمال جميع الرسائل
        const isAllCompleted = totalCompletedMessages >= statisticsData.expectedTotalMessages;

        if (isAllCompleted) {
            statisticsData.status = 'completed';
            statisticsData.endTime = new Date().toISOString();

            // حساب الإجماليات النهائية
            let totalSuccessfulMessages = 0;
            let totalFailedMessages = 0;
            let successfulRecipients = 0;
            let failedRecipients = 0;

            Object.values(statisticsData.recipients).forEach(recipientData => {
                totalSuccessfulMessages += recipientData.successfulMessages;
                totalFailedMessages += recipientData.failedMessages;

                if (recipientData.successfulMessages > 0) {
                    successfulRecipients++;
                } else {
                    failedRecipients++;
                }
            });

            statisticsData.totalSuccessfulMessages = totalSuccessfulMessages;
            statisticsData.totalFailedMessages = totalFailedMessages;
            statisticsData.successfulRecipients = successfulRecipients;
            statisticsData.failedRecipients = failedRecipients;
        }

        // حفظ التحديثات
        fs.writeFileSync(statisticsPath, JSON.stringify(statisticsData, null, 2));

        console.log(`📊 تحديث إحصائيات: ${fullRecipientId} - ${messageType} - ${success ? 'نجح' : 'فشل'}`);
        console.log(`📈 التقدم: ${totalCompletedMessages}/${statisticsData.expectedTotalMessages}`);
        console.log(`🔍 معرف المستلم الأصلي: ${recipientId}`);
        console.log(`🔍 معرف المستلم المعالج: ${fullRecipientId}`);
        console.log(`🔍 نوع المستلم: ${fullRecipientId.includes('@g.us') ? 'مجموعة' : 'جهة اتصال'}`);

        if (isAllCompleted) {
            console.log(`🎉 اكتملت جميع الرسائل! جاري عرض الإحصائيات النهائية...`);
            displayFinalStatistics(sessionId);

            // حذف ملفات op-*.json بعد اكتمال كل شيء
            cleanupOperationFiles();
        }

        return isAllCompleted;

    } catch (error) {
        console.error('خطأ في تحديث ملف الإحصائيات:', error);
        return false;
    }
}

/**
 * تنظيف ملفات العمليات op-*.json
 */
function cleanupOperationFiles() {
    try {
        if (!fs.existsSync(TEMP_DIR)) return;

        const operationFiles = fs.readdirSync(TEMP_DIR).filter(file => file.startsWith('op-') && file.endsWith('.json'));
        let deletedCount = 0;

        operationFiles.forEach(file => {
            try {
                const filePath = path.join(TEMP_DIR, file);
                fs.unlinkSync(filePath);
                deletedCount++;
            } catch (error) {
                console.error(`خطأ في حذف ملف العملية ${file}:`, error);
            }
        });

        if (deletedCount > 0) {
            console.log(`🗑️ تم حذف ${deletedCount} ملف عملية (op-*.json) بعد اكتمال جميع الرسائل`);
        }

    } catch (error) {
        console.error('خطأ في تنظيف ملفات العمليات:', error);
    }
}

/**
 * إنشاء ملف إحصائيات أولي يحتوي على جميع أرقام المستلمين
 * @param {string} sessionId - معرف الجلسة الشاملة
 * @param {Array} recipients - قائمة المستلمين
 * @param {Array} messageTypes - أنواع الرسائل المراد إرسالها
 * @returns {string} مسار ملف الإحصائيات
 */
function initializeStatisticsFile(sessionId, recipients, messageTypes) {
    try {
        const statisticsData = {
            sessionId: sessionId,
            startTime: new Date().toISOString(),
            status: 'started',
            totalRecipients: recipients.length,
            totalMessageTypes: messageTypes.length,
            expectedTotalMessages: recipients.length * messageTypes.length,
            completedMessages: 0,
            recipients: {}
        };

        // إضافة كل مستلم مع معرفاته الصحيحة (جهات الاتصال @c.us والمجموعات @g.us)
        recipients.forEach((recipient, index) => {
            const recipientId = recipient.id || recipient.name || `recipient_${index}`;

            console.log(`🔍 معالجة المستلم ${index + 1}:`, {
                originalRecipient: recipient,
                extractedId: recipientId,
                recipientType: recipient.type
            });

            // تحديد نوع المستلم وتنسيق المعرف بشكل صحيح
            let fullRecipientId;
            if (recipient.type === 'group' || recipientId.includes('@g.us')) {
                // مجموعة
                fullRecipientId = recipientId.includes('@g.us') ? recipientId : `${recipientId}@g.us`;
                console.log(`📱 تم تحديد كمجموعة: ${fullRecipientId}`);
            } else {
                // جهة اتصال عادية
                fullRecipientId = recipientId.includes('@c.us') ? recipientId : `${recipientId}@c.us`;
                console.log(`👤 تم تحديد كجهة اتصال: ${fullRecipientId}`);
            }

            statisticsData.recipients[fullRecipientId] = {
                name: recipient.name || recipientId,
                originalId: recipientId,
                type: recipient.type || (fullRecipientId.includes('@g.us') ? 'group' : 'contact'),
                index: index,
                isLastRecipient: index === recipients.length - 1,
                messageTypes: {},
                totalMessages: messageTypes.length,
                completedMessages: 0,
                successfulMessages: 0,
                failedMessages: 0
            };

            console.log(`✅ تم إضافة المستلم للإحصائيات: ${fullRecipientId}`);

            // تهيئة كل نوع رسالة لهذا المستلم
            messageTypes.forEach(messageType => {
                statisticsData.recipients[fullRecipientId].messageTypes[messageType] = {
                    status: 'pending',
                    success: null,
                    error: null,
                    timestamp: null,
                    operationId: null
                };
            });
        });

        // حفظ الملف
        const statisticsPath = path.join(TEMP_DIR, `statistics_${sessionId}.json`);
        fs.writeFileSync(statisticsPath, JSON.stringify(statisticsData, null, 2));

        console.log(`📊 تم إنشاء ملف الإحصائيات: ${statisticsPath}`);
        console.log(`👥 المستلمين المسجلين: ${recipients.length}`);
        console.log(`📨 إجمالي الرسائل المتوقعة: ${statisticsData.expectedTotalMessages}`);

        return statisticsPath;

    } catch (error) {
        console.error('خطأ في إنشاء ملف الإحصائيات:', error);
        return null;
    }
}

/**
 * تحديث حالة رسالة في ملف الإحصائيات
 * @param {string} sessionId - معرف الجلسة
 * @param {string} recipientId - معرف المستلم (مع @c.us)
 * @param {string} messageType - نوع الرسالة
 * @param {boolean} success - نجح الإرسال أم لا
 * @param {string} error - رسالة الخطأ (إن وجدت)
 * @param {string} operationId - معرف العملية
 * @returns {boolean} هل انتهت جميع الرسائل؟
 */
function updateStatisticsFile(sessionId, recipientId, messageType, success, error = null, operationId = null) {
    try {
        console.log(`🔄 updateStatisticsFile استدعيت مع:`, {
            sessionId: sessionId,
            recipientId: recipientId,
            messageType: messageType,
            success: success,
            error: error,
            operationId: operationId
        });

        const statisticsPath = path.join(TEMP_DIR, `statistics_${sessionId}.json`);

        if (!fs.existsSync(statisticsPath)) {
            console.error(`❌ ملف الإحصائيات غير موجود: ${statisticsPath}`);
            return false;
        }

        const statisticsData = JSON.parse(fs.readFileSync(statisticsPath, 'utf8'));
        console.log(`📊 تم تحميل ملف الإحصائيات، المستلمين المتاحين:`, Object.keys(statisticsData.recipients));

        // التأكد من أن المستلم موجود
        const fullRecipientId = recipientId.includes('@c.us') ? recipientId : `${recipientId}@c.us`;

        if (!statisticsData.recipients[fullRecipientId]) {
            console.error(`المستلم غير موجود في الإحصائيات: ${fullRecipientId}`);
            return false;
        }

        const recipient = statisticsData.recipients[fullRecipientId];

        // تحديث حالة الرسالة
        if (recipient.messageTypes[messageType]) {
            const wasCompleted = recipient.messageTypes[messageType].status === 'completed';

            recipient.messageTypes[messageType] = {
                status: 'completed',
                success: success,
                error: error,
                timestamp: new Date().toISOString(),
                operationId: operationId
            };

            // تحديث العدادات فقط إذا لم تكن مكتملة من قبل
            if (!wasCompleted) {
                recipient.completedMessages++;
                statisticsData.completedMessages++;

                if (success) {
                    recipient.successfulMessages++;
                } else {
                    recipient.failedMessages++;
                }
            }
        }

        // التحقق من اكتمال جميع الرسائل
        const isAllCompleted = statisticsData.completedMessages >= statisticsData.expectedTotalMessages;

        if (isAllCompleted) {
            statisticsData.status = 'completed';
            statisticsData.endTime = new Date().toISOString();
        }

        // حفظ التحديثات
        fs.writeFileSync(statisticsPath, JSON.stringify(statisticsData, null, 2));

        console.log(`📊 تحديث إحصائيات: ${fullRecipientId} - ${messageType} - ${success ? 'نجح' : 'فشل'}`);
        console.log(`📈 التقدم: ${statisticsData.completedMessages}/${statisticsData.expectedTotalMessages}`);
        console.log(`💾 تم حفظ ملف الإحصائيات بنجاح`);

        if (isAllCompleted) {
            console.log(`🎉 اكتملت جميع الرسائل! جاري عرض الإحصائيات النهائية...`);
            displayFinalStatistics(sessionId);
        }

        return isAllCompleted;

    } catch (error) {
        console.error('خطأ في تحديث ملف الإحصائيات:', error);
        return false;
    }
}

/**
 * عرض الإحصائيات النهائية
 * @param {string} sessionId - معرف الجلسة
 */
function displayFinalStatistics(sessionId) {
    try {
        const statisticsPath = path.join(TEMP_DIR, `statistics_${sessionId}.json`);
        const statisticsData = JSON.parse(fs.readFileSync(statisticsPath, 'utf8'));

        const totalSuccessful = Object.values(statisticsData.recipients).reduce((sum, recipient) => sum + recipient.successfulMessages, 0);
        const totalFailed = Object.values(statisticsData.recipients).reduce((sum, recipient) => sum + recipient.failedMessages, 0);
        const successfulRecipients = Object.values(statisticsData.recipients).filter(recipient => recipient.successfulMessages > 0).length;
        const failedRecipients = Object.values(statisticsData.recipients).filter(recipient => recipient.successfulMessages === 0).length;

        console.log(`\n🎉 ===== الإحصائيات النهائية =====`);
        console.log(`📨 تم إرسال ${totalSuccessful} رسالة إلى عدد ${successfulRecipients} مستلم`);
        console.log(`👥 إجمالي المستلمين: ${statisticsData.totalRecipients} مستلم`);
        console.log(`✅ المستلمين الناجحين: ${successfulRecipients}`);
        console.log(`❌ المستلمين الفاشلين: ${failedRecipients}`);
        console.log(`📊 إجمالي الرسائل: ${totalSuccessful + totalFailed} رسالة`);

        if (statisticsData.expectedTotalMessages > 0) {
            const successRate = ((totalSuccessful / statisticsData.expectedTotalMessages) * 100).toFixed(1);
            console.log(`📈 معدل النجاح: ${successRate}%`);
        }

        console.log(`\n📋 تفاصيل المستلمين:`);
        Object.entries(statisticsData.recipients).forEach(([recipientId, data]) => {
            const successRate = data.totalMessages > 0 ? ((data.successfulMessages / data.totalMessages) * 100).toFixed(1) : '0';
            console.log(`  👤 ${data.name} (${recipientId}): ${data.successfulMessages}/${data.totalMessages} رسالة (${successRate}%)`);
        });

        const duration = statisticsData.endTime ?
            ((new Date(statisticsData.endTime) - new Date(statisticsData.startTime)) / 1000).toFixed(1) : 'غير محدد';

        console.log(`\n⏱️ مدة العملية: ${duration} ثانية`);
        console.log(`=======================================\n`);

    } catch (error) {
        console.error('خطأ في عرض الإحصائيات النهائية:', error);
    }
}

module.exports = {
    storeConnectionData,
    getConnectionData,
    updateConnectionData,
    addMessageResult,
    cleanupConnectionData,
    cleanupOldTempFiles,
    saveConnectionData,
    updateOperationCompletionStatus,
    initializeStatisticsFile,
    updateStatisticsFile,
    displayFinalStatistics,
    cleanupOperationFiles
};